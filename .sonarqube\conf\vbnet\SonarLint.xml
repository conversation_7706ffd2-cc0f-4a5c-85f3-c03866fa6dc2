<?xml version="1.0" encoding="UTF-8"?>
<AnalysisInput>
  <Settings>
    <Setting>
      <Key>sonar.vbnet.ignoreHeaderComments</Key>
      <Value>true</Value>
    </Setting>
    <Setting>
      <Key>sonar.vbnet.file.suffixes</Key>
      <Value>.vb</Value>
    </Setting>
    <Setting>
      <Key>sonar.vbnet.roslyn.ignoreIssues</Key>
      <Value>false</Value>
    </Setting>
    <Setting>
      <Key>sonar.vbnet.analyzeGeneratedCode</Key>
      <Value>false</Value>
    </Setting>
  </Settings>
  <Rules>
    <Rule>
      <Key>S1048</Key>
    </Rule>
    <Rule>
      <Key>S1066</Key>
    </Rule>
    <Rule>
      <Key>S1110</Key>
    </Rule>
    <Rule>
      <Key>S1123</Key>
    </Rule>
    <Rule>
      <Key>S1125</Key>
    </Rule>
    <Rule>
      <Key>S1133</Key>
    </Rule>
    <Rule>
      <Key>S1134</Key>
    </Rule>
    <Rule>
      <Key>S1135</Key>
    </Rule>
    <Rule>
      <Key>S1155</Key>
    </Rule>
    <Rule>
      <Key>S1163</Key>
    </Rule>
    <Rule>
      <Key>S1172</Key>
    </Rule>
    <Rule>
      <Key>S1186</Key>
    </Rule>
    <Rule>
      <Key>S1479</Key>
      <Parameters>
        <Parameter>
          <Key>maximum</Key>
          <Value>30</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1481</Key>
    </Rule>
    <Rule>
      <Key>S1862</Key>
    </Rule>
    <Rule>
      <Key>S1940</Key>
    </Rule>
    <Rule>
      <Key>S1944</Key>
    </Rule>
    <Rule>
      <Key>S2225</Key>
    </Rule>
    <Rule>
      <Key>S2234</Key>
    </Rule>
    <Rule>
      <Key>S2257</Key>
    </Rule>
    <Rule>
      <Key>S2340</Key>
    </Rule>
    <Rule>
      <Key>S2342</Key>
      <Parameters>
        <Parameter>
          <Key>flagsAttributeFormat</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?s$</Value>
        </Parameter>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2344</Key>
    </Rule>
    <Rule>
      <Key>S2345</Key>
    </Rule>
    <Rule>
      <Key>S2346</Key>
    </Rule>
    <Rule>
      <Key>S2347</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^(([a-z][a-z0-9]*)?([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?_)?([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2349</Key>
    </Rule>
    <Rule>
      <Key>S2352</Key>
    </Rule>
    <Rule>
      <Key>S2355</Key>
    </Rule>
    <Rule>
      <Key>S2358</Key>
    </Rule>
    <Rule>
      <Key>S2359</Key>
    </Rule>
    <Rule>
      <Key>S2365</Key>
    </Rule>
    <Rule>
      <Key>S2368</Key>
    </Rule>
    <Rule>
      <Key>S2372</Key>
    </Rule>
    <Rule>
      <Key>S2375</Key>
      <Parameters>
        <Parameter>
          <Key>minimumSeriesLength</Key>
          <Value>6</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2376</Key>
    </Rule>
    <Rule>
      <Key>S2692</Key>
    </Rule>
    <Rule>
      <Key>S2925</Key>
    </Rule>
    <Rule>
      <Key>S2951</Key>
    </Rule>
    <Rule>
      <Key>S3358</Key>
    </Rule>
    <Rule>
      <Key>S3363</Key>
    </Rule>
    <Rule>
      <Key>S3431</Key>
    </Rule>
    <Rule>
      <Key>S3449</Key>
    </Rule>
    <Rule>
      <Key>S3453</Key>
    </Rule>
    <Rule>
      <Key>S3464</Key>
    </Rule>
    <Rule>
      <Key>S3466</Key>
    </Rule>
    <Rule>
      <Key>S3598</Key>
    </Rule>
    <Rule>
      <Key>S3776</Key>
      <Parameters>
        <Parameter>
          <Key>threshold</Key>
          <Value>15</Value>
        </Parameter>
        <Parameter>
          <Key>propertyThreshold</Key>
          <Value>3</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S3903</Key>
    </Rule>
    <Rule>
      <Key>S3904</Key>
    </Rule>
    <Rule>
      <Key>S3923</Key>
    </Rule>
    <Rule>
      <Key>S3926</Key>
    </Rule>
    <Rule>
      <Key>S3927</Key>
    </Rule>
    <Rule>
      <Key>S4201</Key>
    </Rule>
    <Rule>
      <Key>S4210</Key>
    </Rule>
    <Rule>
      <Key>S4423</Key>
    </Rule>
    <Rule>
      <Key>S4428</Key>
    </Rule>
    <Rule>
      <Key>S4507</Key>
    </Rule>
    <Rule>
      <Key>S4545</Key>
    </Rule>
    <Rule>
      <Key>S4663</Key>
    </Rule>
    <Rule>
      <Key>S4790</Key>
    </Rule>
    <Rule>
      <Key>S5042</Key>
    </Rule>
    <Rule>
      <Key>S5542</Key>
    </Rule>
    <Rule>
      <Key>S5547</Key>
    </Rule>
    <Rule>
      <Key>S5659</Key>
    </Rule>
    <Rule>
      <Key>S5753</Key>
    </Rule>
    <Rule>
      <Key>S5856</Key>
    </Rule>
    <Rule>
      <Key>S6145</Key>
    </Rule>
    <Rule>
      <Key>S6146</Key>
    </Rule>
    <Rule>
      <Key>S6607</Key>
    </Rule>
    <Rule>
      <Key>S6608</Key>
    </Rule>
    <Rule>
      <Key>S6609</Key>
    </Rule>
    <Rule>
      <Key>S6610</Key>
    </Rule>
    <Rule>
      <Key>S6612</Key>
    </Rule>
    <Rule>
      <Key>S6613</Key>
    </Rule>
    <Rule>
      <Key>S6617</Key>
    </Rule>
    <Rule>
      <Key>S6930</Key>
    </Rule>
    <Rule>
      <Key>S6931</Key>
    </Rule>
    <Rule>
      <Key>S927</Key>
    </Rule>
    <Rule>
      <Key>S101</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S107</Key>
      <Parameters>
        <Parameter>
          <Key>max</Key>
          <Value>7</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1075</Key>
    </Rule>
    <Rule>
      <Key>S108</Key>
    </Rule>
    <Rule>
      <Key>S112</Key>
    </Rule>
    <Rule>
      <Key>S114</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^I([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S117</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^[a-z][a-z0-9]*([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1192</Key>
      <Parameters>
        <Parameter>
          <Key>threshold</Key>
          <Value>3</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1313</Key>
    </Rule>
    <Rule>
      <Key>S1542</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1643</Key>
    </Rule>
    <Rule>
      <Key>S1645</Key>
    </Rule>
    <Rule>
      <Key>S1654</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^[a-z][a-z0-9]*([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1656</Key>
    </Rule>
    <Rule>
      <Key>S1751</Key>
    </Rule>
    <Rule>
      <Key>S1764</Key>
    </Rule>
    <Rule>
      <Key>S1871</Key>
    </Rule>
    <Rule>
      <Key>S2068</Key>
      <Parameters>
        <Parameter>
          <Key>credentialWords</Key>
          <Value>password, passwd, pwd, passphrase</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2077</Key>
    </Rule>
    <Rule>
      <Key>S2094</Key>
    </Rule>
    <Rule>
      <Key>S2166</Key>
    </Rule>
    <Rule>
      <Key>S2178</Key>
    </Rule>
    <Rule>
      <Key>S2304</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?(\.([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?)*$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2437</Key>
    </Rule>
    <Rule>
      <Key>S2551</Key>
    </Rule>
    <Rule>
      <Key>S2612</Key>
    </Rule>
    <Rule>
      <Key>S2737</Key>
    </Rule>
    <Rule>
      <Key>S2757</Key>
    </Rule>
    <Rule>
      <Key>S2761</Key>
    </Rule>
    <Rule>
      <Key>S3011</Key>
    </Rule>
    <Rule>
      <Key>S3063</Key>
    </Rule>
    <Rule>
      <Key>S3385</Key>
    </Rule>
    <Rule>
      <Key>S3603</Key>
    </Rule>
    <Rule>
      <Key>S3869</Key>
    </Rule>
    <Rule>
      <Key>S3871</Key>
    </Rule>
    <Rule>
      <Key>S3878</Key>
    </Rule>
    <Rule>
      <Key>S3889</Key>
    </Rule>
    <Rule>
      <Key>S3981</Key>
    </Rule>
    <Rule>
      <Key>S3998</Key>
    </Rule>
    <Rule>
      <Key>S4036</Key>
    </Rule>
    <Rule>
      <Key>S4136</Key>
    </Rule>
    <Rule>
      <Key>S4143</Key>
    </Rule>
    <Rule>
      <Key>S4144</Key>
    </Rule>
    <Rule>
      <Key>S4159</Key>
    </Rule>
    <Rule>
      <Key>S4260</Key>
    </Rule>
    <Rule>
      <Key>S4275</Key>
    </Rule>
    <Rule>
      <Key>S4277</Key>
    </Rule>
    <Rule>
      <Key>S4581</Key>
    </Rule>
    <Rule>
      <Key>S4583</Key>
    </Rule>
    <Rule>
      <Key>S4586</Key>
    </Rule>
    <Rule>
      <Key>S4830</Key>
    </Rule>
    <Rule>
      <Key>S5443</Key>
    </Rule>
    <Rule>
      <Key>S5445</Key>
    </Rule>
    <Rule>
      <Key>S5693</Key>
      <Parameters>
        <Parameter>
          <Key>fileUploadSizeLimit</Key>
          <Value>8388608</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S5944</Key>
    </Rule>
    <Rule>
      <Key>S6444</Key>
    </Rule>
    <Rule>
      <Key>S6561</Key>
    </Rule>
    <Rule>
      <Key>S6562</Key>
    </Rule>
    <Rule>
      <Key>S6575</Key>
    </Rule>
    <Rule>
      <Key>S6580</Key>
    </Rule>
    <Rule>
      <Key>S6588</Key>
    </Rule>
    <Rule>
      <Key>S907</Key>
    </Rule>
  </Rules>
  <Files>
  </Files>
</AnalysisInput>
