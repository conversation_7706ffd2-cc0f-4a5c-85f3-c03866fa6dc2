﻿C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Android\Resources\values\colors.xml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\App.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\AppShell.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Configuration\AppSettings.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Configuration\BuildConfiguration.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Configuration\LogSettingModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Configuration\MedicalCheckProgressSetting.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Configuration\Service\AppSettingServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Configuration\Service\IAppSettingServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\ClientInformationModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\ExamineAfterAuthModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\MedicalCheckStateModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\NoteMasterModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\NoteModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\ScrollPositionModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\SexModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\StayFlagModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Models\TestStatusModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Pages\ExamineAfterAuthPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Pages\ExamineAfterAuthViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Pages\ExamineBeforeAuthPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Pages\ExamineBeforeAuthViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Services\ExamineService.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Services\IExamineService.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\BackButtonView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationAfterAuthEntryView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationAfterAuthView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationBarView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationFunctionButtonView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationTableView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\HomeButtonView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\LoginUserView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\MaskButtonView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\NoteEditPopup.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\PatientInfoView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\PatientListMonitorView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\PatientListView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Models\GuideSelection.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Models\MedicalCheckModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceAdjustmentPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceAdjustmentViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceSelectionPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceSelectionViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceSettingPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceSettingViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Services\GuidanceAdjustmentService.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Services\IGuidanceAdjustmentService.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Views\GuidanceSelectionListView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Views\PatientGuidanceFilterView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Views\PatientGuidanceTableView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Models\MenuItem.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Pages\BlankPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Pages\BlankPageViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Pages\HomePage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Pages\HomePageViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Models\IndividualPatientInfoDto.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Models\IndividualPatientModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Pages\IndividualProgressPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Pages\IndividualProgressViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Services\IIndividualProgressServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Services\IndividualProgressServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualPatientLabelView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualProgessPatientsInfo.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualProgressPatientDetail.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualSectionView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualStateCounterView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualStateDisplayView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualTableView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Login\Models\LoginModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Login\Pages\LoginPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Login\Pages\LoginPageViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Models\ClientInformationCheckedModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Models\MedicalCheckListModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Pages\PatientPendingPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Pages\PatientPendingPageViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Views\PatientPendingTableView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\StopExamine\Models\MedicalChecklistReasonModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\StopExamine\Models\MedicalCheckStateStopModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\StopExamine\Pages\StopExaminePage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\StopExamine\Pages\StopExaminePageViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Models\MedicalChecklist.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\PendingTestSelectionPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\PendingTestSelectionViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\StopTestSelectionPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\StopTestSelectionViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\TestSelectionPage.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\TestSelectionPageViewModel.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Views\SelectionTestStateListView.xaml.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\GlobalUsing.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Helpers\AppDictionaryHelpers.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Helpers\RouteHelpers.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Mapper\Config\ConfigurationAutoMapper.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Mapper\Profile\MapperProfile.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\MauiProgram.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Android\MainActivity.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Android\MainApplication.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Fonts\FluentUI.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\RouterName.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Services\IBarcodeScannerServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Services\ISessionServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Services\SessionServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Services\WindowBarcodeScannerServices.cs
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\App.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\AppShell.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Pages\ExamineAfterAuthPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Pages\ExamineBeforeAuthPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\BackButtonView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationAfterAuthEntryView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationAfterAuthView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationBarView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationFunctionButtonView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\ExaminationTableView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\HomeButtonView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\LoginUserView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\MaskButtonView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\NoteEditPopup.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\PatientInfoView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\PatientListMonitorView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Examine\Views\PatientListView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceAdjustmentPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceSelectionPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Pages\GuidanceSettingPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Views\GuidanceSelectionListView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Views\PatientGuidanceFilterView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Guidance\Views\PatientGuidanceTableView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Pages\BlankPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Home\Pages\HomePage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Pages\IndividualProgressPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualPatientLabelView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualProgessPatientsInfo.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualProgressPatientDetail.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualSectionView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualStateCounterView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualStateDisplayView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\IndividualProgress\Views\IndividualTableView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\Login\Pages\LoginPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Pages\PatientPendingPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\PatientPending\Views\PatientPendingTableView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\StopExamine\Pages\StopExaminePage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\PendingTestSelectionPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\StopTestSelectionPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Pages\TestSelectionPage.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Features\TestSelection\Views\SelectionTestStateListView.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Styles\Colors.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Styles\Styles.xaml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\appsettings.json
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Android\AndroidManifest.xml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\iOS\Info.plist
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\iOS\Resources\PrivacyInfo.xcprivacy
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\MacCatalyst\Entitlements.plist
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\MacCatalyst\Info.plist
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Tizen\tizen-manifest.xml
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Windows\app.manifest
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Platforms\Windows\Package.appxmanifest
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Properties\launchSettings.json
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\AppIcon\appicon.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\AppIcon\appiconfg.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Fonts\OpenSans-Regular.ttf
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Fonts\OpenSans-Semibold.ttf
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\add_icon.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\arrow_circle_left_24x24.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\arrow_circle_right_24x24.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\arrow_down.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\back_door_icon.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\back_icon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\ban_icon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\bulkholdscreen.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\cancel.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\change_guide.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\circle_24x24.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\close_40x40.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\close_icon.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\direction_down.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\dotnet_bot.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\guidancescreen.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\home_40x40.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\home_icon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\icon_arrow_chevron_right.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\individualicon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\logout.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\medicalcheckimage.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\pause_circle.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\pending_all.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\play_circle.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\power_off_40x40.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\progressicon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\save.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\save_icon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\search.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\search_24x24.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\shake_hand.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\table_icon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\undo_40x40.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\update.svg
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\varioussettingsicon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\visibility.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\visibility_off.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Images\x_icon.png
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Raw\AboutAssets.txt
C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun\Resources\Splash\splash.svg
