{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldMsg' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 10, "startColumn": 12, "endLine": 10, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 6, "startColumn": 20, "endLine": 6, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldMsg' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 33, "startColumn": 12, "endLine": 33, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 6, "startColumn": 20, "endLine": 6, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldData' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 20, "startColumn": 12, "endLine": 20, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 8, "startColumn": 22, "endLine": 8, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldHashTable' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 20, "startColumn": 12, "endLine": 20, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 12, "startColumn": 23, "endLine": 12, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldReceiveData' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 20, "startColumn": 12, "endLine": 20, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 16, "startColumn": 20, "endLine": 16, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldData' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 28, "startColumn": 12, "endLine": 28, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 8, "startColumn": 22, "endLine": 8, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldReceiveData' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 28, "startColumn": 12, "endLine": 28, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 16, "startColumn": 20, "endLine": 16, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'Attribute' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 114, "startColumn": 12, "endLine": 114, "endColumn": 33}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 6, "startColumn": 20, "endLine": 6, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldMsg' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 11, "startColumn": 12, "endLine": 11, "endColumn": 40}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 7, "startColumn": 20, "endLine": 7, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldMsg' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 31, "startColumn": 12, "endLine": 31, "endColumn": 40}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 7, "startColumn": 20, "endLine": 7, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 149, "startColumn": 65, "endLine": 149, "endColumn": 69}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'comSC' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 137, "startColumn": 32, "endLine": 137, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'Ana' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 139, "startColumn": 30, "endLine": 139, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'comCA' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 141, "startColumn": 34, "endLine": 141, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'ConnectSetting' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 142, "startColumn": 18, "endLine": 142, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'ReceiveSetting' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 143, "startColumn": 18, "endLine": 143, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'ReceiveDataConfig' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 144, "startColumn": 18, "endLine": 144, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'SendSetting' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 145, "startColumn": 18, "endLine": 145, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'SendDataConfig' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 146, "startColumn": 18, "endLine": 146, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'ObjectFromResult' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 12, "endLine": 160, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 156, "startColumn": 19, "endLine": 156, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldMsg' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 11, "startColumn": 12, "endLine": 11, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 7, "startColumn": 20, "endLine": 7, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FldMsg' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 33, "startColumn": 12, "endLine": 33, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 7, "startColumn": 20, "endLine": 7, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 125, "startColumn": 58, "endLine": 125, "endColumn": 70}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 184, "startColumn": 50, "endLine": 184, "endColumn": 62}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 425, "startColumn": 28, "endLine": 425, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 456, "startColumn": 20, "endLine": 456, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 476, "startColumn": 28, "endLine": 476, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 481, "startColumn": 42, "endLine": 481, "endColumn": 54}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 486, "startColumn": 42, "endLine": 486, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 269, "startColumn": 29, "endLine": 269, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 277, "startColumn": 29, "endLine": 277, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 277, "startColumn": 61, "endLine": 277, "endColumn": 83}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 285, "startColumn": 29, "endLine": 285, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 293, "startColumn": 29, "endLine": 293, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 301, "startColumn": 29, "endLine": 301, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 533, "startColumn": 22, "endLine": 533, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 534, "startColumn": 20, "endLine": 534, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 417, "startColumn": 25, "endLine": 417, "endColumn": 47}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8605", "level": "warning", "message": "Unboxing a possibly null value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 424, "startColumn": 30, "endLine": 424, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 362, "startColumn": 17, "endLine": 362, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 367, "startColumn": 17, "endLine": 367, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 372, "startColumn": 17, "endLine": 372, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 382, "startColumn": 17, "endLine": 382, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 387, "startColumn": 17, "endLine": 387, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 392, "startColumn": 17, "endLine": 392, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 566, "startColumn": 9, "endLine": 566, "endColumn": 58}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4663", "level": "warning", "message": "Remove this empty comment", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 146, "startColumn": 13, "endLine": 146, "endColumn": 16}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonDataAnalyze.cs", "region": {"startLine": 62, "startColumn": 13, "endLine": 62, "endColumn": 48}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 27, "startColumn": 9, "endLine": 27, "endColumn": 75}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 170, "startColumn": 17, "endLine": 170, "endColumn": 72}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 250, "startColumn": 21, "endLine": 250, "endColumn": 71}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 7, "startColumn": 39, "endLine": 7, "endColumn": 50}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 9, "startColumn": 17, "endLine": 9, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'FldMsg' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 7, "startColumn": 20, "endLine": 7, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4136", "level": "warning", "message": "All 'ErrorDataException' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 11, "startColumn": 12, "endLine": 11, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 33, "startColumn": 12, "endLine": 33, "endColumn": 30}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S1192", "level": "warning", "message": "Define a constant instead of using this literal '【SerialConnect】' 9 times.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 112, "startColumn": 46, "endLine": 112, "endColumn": 63}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 118, "startColumn": 54, "endLine": 118, "endColumn": 71}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 131, "startColumn": 58, "endLine": 131, "endColumn": 75}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 139, "startColumn": 46, "endLine": 139, "endColumn": 63}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 169, "startColumn": 50, "endLine": 169, "endColumn": 67}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 174, "startColumn": 54, "endLine": 174, "endColumn": 71}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 178, "startColumn": 54, "endLine": 178, "endColumn": 71}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 183, "startColumn": 50, "endLine": 183, "endColumn": 67}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 190, "startColumn": 46, "endLine": 190, "endColumn": 63}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null}}}, {"ruleId": "S1192", "level": "warning", "message": "Define a constant instead of using this literal 'Port_Open:' 4 times.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 112, "startColumn": 65, "endLine": 112, "endColumn": 77}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 118, "startColumn": 73, "endLine": 118, "endColumn": 85}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 131, "startColumn": 77, "endLine": 131, "endColumn": 89}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 139, "startColumn": 65, "endLine": 139, "endColumn": 77}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null, "1": null, "2": null}}}, {"ruleId": "S1192", "level": "warning", "message": "Define a constant instead of using this literal 'Port_Close:' 5 times.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 169, "startColumn": 69, "endLine": 169, "endColumn": 82}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 174, "startColumn": 73, "endLine": 174, "endColumn": 86}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 178, "startColumn": 73, "endLine": 178, "endColumn": 86}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 183, "startColumn": 69, "endLine": 183, "endColumn": 82}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 190, "startColumn": 65, "endLine": 190, "endColumn": 78}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null, "1": null, "2": null, "3": null}}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/ErrorDataException.cs", "region": {"startLine": 40, "startColumn": 16, "endLine": 40, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/Const/MeItemCodeConst.cs", "region": {"startLine": 2, "startColumn": 14, "endLine": 2, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 142, "startColumn": 18, "endLine": 142, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 139, "startColumn": 30, "endLine": 139, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 141, "startColumn": 34, "endLine": 141, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 144, "startColumn": 18, "endLine": 144, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 145, "startColumn": 18, "endLine": 145, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 143, "startColumn": 18, "endLine": 143, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 146, "startColumn": 18, "endLine": 146, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 156, "startColumn": 19, "endLine": 156, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'FldMsg' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 7, "startColumn": 20, "endLine": 7, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'Logger' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 147, "startColumn": 26, "endLine": 147, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4136", "level": "warning", "message": "All 'MeConnectNonSettingException' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 11, "startColumn": 12, "endLine": 11, "endColumn": 40}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 31, "startColumn": 12, "endLine": 31, "endColumn": 40}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/MeConnectNonSettingException.cs", "region": {"startLine": 37, "startColumn": 16, "endLine": 37, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'FldMsg' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 6, "startColumn": 20, "endLine": 6, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 40, "startColumn": 16, "endLine": 40, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4136", "level": "warning", "message": "All 'SendErrorException' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 10, "startColumn": 12, "endLine": 10, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/SendErrorException.cs", "region": {"startLine": 33, "startColumn": 12, "endLine": 33, "endColumn": 30}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 43, "startColumn": 21, "endLine": 43, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 57, "startColumn": 22, "endLine": 57, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/RcvDataSet.cs", "region": {"startLine": 71, "startColumn": 19, "endLine": 71, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/TimeOutException.cs", "region": {"startLine": 22, "startColumn": 16, "endLine": 22, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 128, "startColumn": 21, "endLine": 128, "endColumn": 56}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CancelException.cs", "region": {"startLine": 15, "startColumn": 16, "endLine": 15, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 20, "startColumn": 21, "endLine": 63, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 66, "startColumn": 21, "endLine": 109, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 137, "startColumn": 32, "endLine": 137, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3442", "level": "warning", "message": "Change the visibility of this constructor to 'protected'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 160, "startColumn": 5, "endLine": 160, "endColumn": 11}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'Com_DataAnalyze' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonDataAnalyze.cs", "region": {"startLine": 15, "startColumn": 21, "endLine": 15, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Open()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 69, "startColumn": 13, "endLine": 69, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DataBits' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 22, "startColumn": 9, "endLine": 22, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 73, "startColumn": 13, "endLine": 73, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 61, "startColumn": 13, "endLine": 61, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 39, "startColumn": 13, "endLine": 39, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 67, "startColumn": 14, "endLine": 67, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 63, "startColumn": 13, "endLine": 63, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.XOnXOff' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 43, "startColumn": 37, "endLine": 43, "endColumn": 70}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.None' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 31, "startColumn": 37, "endLine": 31, "endColumn": 67}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DtrEnable' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 48, "startColumn": 9, "endLine": 48, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 43, "startColumn": 13, "endLine": 43, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadTimeout' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 51, "startColumn": 9, "endLine": 51, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 18, "startColumn": 23, "endLine": 18, "endColumn": 61}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Parity' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 26, "startColumn": 9, "endLine": 26, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 35, "startColumn": 13, "endLine": 35, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.RequestToSend' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 35, "startColumn": 37, "endLine": 35, "endColumn": 76}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.RtsEnable' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 46, "startColumn": 9, "endLine": 46, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 31, "startColumn": 13, "endLine": 31, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.BaudRate' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 20, "startColumn": 9, "endLine": 20, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'Com_DataAnalyze' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonDataAnalyze.cs", "region": {"startLine": 15, "startColumn": 21, "endLine": 15, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.RequestToSendXOnXOff' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 39, "startColumn": 37, "endLine": 39, "endColumn": 83}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.StopBits' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 24, "startColumn": 9, "endLine": 24, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'dataSize'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonDataAnalyze.cs", "region": {"startLine": 53, "startColumn": 21, "endLine": 53, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'dataSize'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonDataAnalyze.cs", "region": {"startLine": 73, "startColumn": 21, "endLine": 73, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 303, "startColumn": 24, "endLine": 303, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 26 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 120, "startColumn": 19, "endLine": 120, "endColumn": 38}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 128, "startColumn": 9, "endLine": 128, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 134, "startColumn": 9, "endLine": 134, "endColumn": 12}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 149, "startColumn": 13, "endLine": 149, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 149, "startColumn": 30, "endLine": 149, "endColumn": 32}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 153, "startColumn": 13, "endLine": 153, "endColumn": 17}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 155, "startColumn": 17, "endLine": 155, "endColumn": 20}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 161, "startColumn": 13, "endLine": 161, "endColumn": 17}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 163, "startColumn": 17, "endLine": 163, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 167, "startColumn": 17, "endLine": 167, "endColumn": 21}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 169, "startColumn": 21, "endLine": 169, "endColumn": 24}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 176, "startColumn": 13, "endLine": 176, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 182, "startColumn": 9, "endLine": 182, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 188, "startColumn": 9, "endLine": 188, "endColumn": 13}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 196, "startColumn": 9, "endLine": 196, "endColumn": 13}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 202, "startColumn": 9, "endLine": 202, "endColumn": 13}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 208, "startColumn": 9, "endLine": 208, "endColumn": 13}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 213, "startColumn": 9, "endLine": 213, "endColumn": 13}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+1", "10": "+2 (incl 1 for nesting)", "11": "+1", "12": "+1", "13": "+1", "14": "+1", "15": "+1", "16": "+1", "2": "+2 (incl 1 for nesting)", "3": "+1", "4": "+1", "5": "+3 (incl 2 for nesting)", "6": "+1", "7": "+3 (incl 2 for nesting)", "8": "+1", "9": "+4 (incl 3 for nesting)"}}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 150, "startColumn": 13, "endLine": 151, "endColumn": 14}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 109, "startColumn": 17, "endLine": 109, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 20 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 89, "startColumn": 17, "endLine": 89, "endColumn": 37}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 94, "startColumn": 9, "endLine": 94, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 99, "startColumn": 9, "endLine": 99, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 103, "startColumn": 13, "endLine": 103, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 109, "startColumn": 17, "endLine": 109, "endColumn": 22}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 113, "startColumn": 13, "endLine": 113, "endColumn": 18}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 121, "startColumn": 17, "endLine": 121, "endColumn": 22}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 123, "startColumn": 21, "endLine": 123, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 128, "startColumn": 21, "endLine": 128, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 137, "startColumn": 9, "endLine": 137, "endColumn": 13}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 142, "startColumn": 9, "endLine": 142, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 146, "startColumn": 9, "endLine": 146, "endColumn": 13}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+1", "10": "+1", "2": "+2 (incl 1 for nesting)", "3": "+3 (incl 2 for nesting)", "4": "+2 (incl 1 for nesting)", "5": "+3 (incl 2 for nesting)", "6": "+4 (incl 3 for nesting)", "7": "+1", "8": "+1", "9": "+1"}}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 109, "startColumn": 23, "endLine": 109, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 126, "startColumn": 25, "endLine": 126, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 186, "startColumn": 17, "endLine": 186, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.None' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 182, "startColumn": 47, "endLine": 182, "endColumn": 77}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 190, "startColumn": 17, "endLine": 190, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.RequestToSend' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 186, "startColumn": 47, "endLine": 186, "endColumn": 86}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.StopBits' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 178, "startColumn": 13, "endLine": 178, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DataBits' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 177, "startColumn": 13, "endLine": 177, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.RequestToSendXOnXOff' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 190, "startColumn": 47, "endLine": 190, "endColumn": 93}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'Handshake.XOnXOff' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 194, "startColumn": 47, "endLine": 194, "endColumn": 80}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 182, "startColumn": 17, "endLine": 182, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Handshake' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 194, "startColumn": 17, "endLine": 194, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.BaudRate' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 176, "startColumn": 13, "endLine": 176, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Parity' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 179, "startColumn": 13, "endLine": 179, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 126, "startColumn": 25, "endLine": 126, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 17 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 422, "startColumn": 23, "endLine": 422, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 435, "startColumn": 9, "endLine": 435, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 441, "startColumn": 9, "endLine": 441, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 447, "startColumn": 9, "endLine": 447, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 453, "startColumn": 9, "endLine": 453, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 458, "startColumn": 9, "endLine": 458, "endColumn": 13}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 466, "startColumn": 17, "endLine": 466, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 470, "startColumn": 17, "endLine": 470, "endColumn": 21}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 474, "startColumn": 17, "endLine": 474, "endColumn": 21}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 479, "startColumn": 13, "endLine": 479, "endColumn": 18}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 484, "startColumn": 13, "endLine": 484, "endColumn": 18}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 487, "startColumn": 17, "endLine": 487, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 491, "startColumn": 17, "endLine": 491, "endColumn": 21}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+1", "10": "+3 (incl 2 for nesting)", "11": "+1", "2": "+1", "3": "+1", "4": "+1", "5": "+2 (incl 1 for nesting)", "6": "+1", "7": "+1", "8": "+2 (incl 1 for nesting)", "9": "+2 (incl 1 for nesting)"}}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 185, "startColumn": 17, "endLine": 185, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 438, "startColumn": 13, "endLine": 438, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 444, "startColumn": 13, "endLine": 444, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Open()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 117, "startColumn": 21, "endLine": 117, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 99, "startColumn": 14, "endLine": 99, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 118, "startColumn": 88, "endLine": 118, "endColumn": 108}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 94, "startColumn": 13, "endLine": 94, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 96, "startColumn": 13, "endLine": 96, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 131, "startColumn": 92, "endLine": 131, "endColumn": 112}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 142, "startColumn": 13, "endLine": 142, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 139, "startColumn": 80, "endLine": 139, "endColumn": 100}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 112, "startColumn": 80, "endLine": 112, "endColumn": 100}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 231, "startColumn": 32, "endLine": 231, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 248, "startColumn": 32, "endLine": 248, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 311, "startColumn": 29, "endLine": 311, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 185, "startColumn": 17, "endLine": 185, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 54 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 213, "startColumn": 19, "endLine": 213, "endColumn": 30}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 227, "startColumn": 9, "endLine": 227, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 231, "startColumn": 17, "endLine": 231, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 234, "startColumn": 21, "endLine": 234, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 236, "startColumn": 25, "endLine": 236, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 242, "startColumn": 21, "endLine": 242, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 248, "startColumn": 17, "endLine": 248, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 254, "startColumn": 21, "endLine": 254, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 259, "startColumn": 21, "endLine": 259, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 261, "startColumn": 25, "endLine": 261, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 267, "startColumn": 21, "endLine": 267, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 269, "startColumn": 25, "endLine": 269, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 275, "startColumn": 21, "endLine": 275, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 277, "startColumn": 25, "endLine": 277, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 277, "startColumn": 58, "endLine": 277, "endColumn": 60}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 283, "startColumn": 21, "endLine": 283, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 285, "startColumn": 25, "endLine": 285, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 291, "startColumn": 21, "endLine": 291, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 293, "startColumn": 25, "endLine": 293, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 299, "startColumn": 21, "endLine": 299, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 301, "startColumn": 25, "endLine": 301, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 308, "startColumn": 13, "endLine": 308, "endColumn": 18}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 311, "startColumn": 17, "endLine": 311, "endColumn": 19}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+2 (incl 1 for nesting)", "10": "+4 (incl 3 for nesting)", "11": "+1", "12": "+4 (incl 3 for nesting)", "13": "+1", "14": "+1", "15": "+4 (incl 3 for nesting)", "16": "+1", "17": "+4 (incl 3 for nesting)", "18": "+1", "19": "+4 (incl 3 for nesting)", "2": "+3 (incl 2 for nesting)", "20": "+2 (incl 1 for nesting)", "21": "+3 (incl 2 for nesting)", "3": "+4 (incl 3 for nesting)", "4": "+1", "5": "+2 (incl 1 for nesting)", "6": "+3 (incl 2 for nesting)", "7": "+3 (incl 2 for nesting)", "8": "+4 (incl 3 for nesting)", "9": "+1"}}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'file'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 534, "startColumn": 13, "endLine": 534, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'writer'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 533, "startColumn": 13, "endLine": 533, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 541, "startColumn": 13, "endLine": 541, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 545, "startColumn": 13, "endLine": 545, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 549, "startColumn": 13, "endLine": 549, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 192, "startColumn": 13, "endLine": 192, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 183, "startColumn": 85, "endLine": 183, "endColumn": 105}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 178, "startColumn": 89, "endLine": 178, "endColumn": 109}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 174, "startColumn": 89, "endLine": 174, "endColumn": 109}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 169, "startColumn": 85, "endLine": 169, "endColumn": 105}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 172, "startColumn": 22, "endLine": 172, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Close()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 171, "startColumn": 17, "endLine": 171, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.IsOpen' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 165, "startColumn": 13, "endLine": 165, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 160, "startColumn": 13, "endLine": 160, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 162, "startColumn": 13, "endLine": 162, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.PortName' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 190, "startColumn": 81, "endLine": 190, "endColumn": 101}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 438, "startColumn": 13, "endLine": 438, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 444, "startColumn": 13, "endLine": 444, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 541, "startColumn": 13, "endLine": 541, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 545, "startColumn": 13, "endLine": 545, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/InterfaceLibrary.cs", "region": {"startLine": 549, "startColumn": 13, "endLine": 549, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1066", "level": "warning", "message": "Merge this if statement with the enclosing one.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 301, "startColumn": 25, "endLine": 301, "endColumn": 27}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 299, "startColumn": 26, "endLine": 299, "endColumn": 28}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Merge this if statement with its nested one."}}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadTimeout' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 333, "startColumn": 9, "endLine": 333, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3626", "level": "warning", "message": "Remove this redundant jump.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 315, "startColumn": 17, "endLine": 315, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'birthdayArea'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 407, "startColumn": 16, "endLine": 407, "endColumn": 49}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardOutBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 348, "startColumn": 13, "endLine": 348, "endColumn": 43}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Write(byte[], int, int)' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 349, "startColumn": 13, "endLine": 349, "endColumn": 60}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 442, "startColumn": 33, "endLine": 442, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Write(string)' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 371, "startColumn": 13, "endLine": 371, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardOutBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 370, "startColumn": 13, "endLine": 370, "endColumn": 43}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 18 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 388, "startColumn": 17, "endLine": 388, "endColumn": 33}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 411, "startColumn": 13, "endLine": 411, "endColumn": 18}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 417, "startColumn": 21, "endLine": 417, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 424, "startColumn": 21, "endLine": 424, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 427, "startColumn": 25, "endLine": 427, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 440, "startColumn": 17, "endLine": 440, "endColumn": 22}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 442, "startColumn": 21, "endLine": 442, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 446, "startColumn": 21, "endLine": 446, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 459, "startColumn": 9, "endLine": 459, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 463, "startColumn": 9, "endLine": 463, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 467, "startColumn": 9, "endLine": 467, "endColumn": 14}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+2 (incl 1 for nesting)", "2": "+1", "3": "+3 (incl 2 for nesting)", "4": "+2 (incl 1 for nesting)", "5": "+3 (incl 2 for nesting)", "6": "+3 (incl 2 for nesting)", "7": "+1", "8": "+1", "9": "+1"}}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 20 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 440, "startColumn": 18, "endLine": 440, "endColumn": 48}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 450, "startColumn": 9, "endLine": 450, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 456, "startColumn": 9, "endLine": 456, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 458, "startColumn": 13, "endLine": 458, "endColumn": 16}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 460, "startColumn": 17, "endLine": 460, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 464, "startColumn": 17, "endLine": 464, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 472, "startColumn": 13, "endLine": 472, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 475, "startColumn": 17, "endLine": 475, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 479, "startColumn": 17, "endLine": 479, "endColumn": 21}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 486, "startColumn": 13, "endLine": 486, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 496, "startColumn": 9, "endLine": 496, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 500, "startColumn": 9, "endLine": 500, "endColumn": 13}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+1", "10": "+1", "2": "+2 (incl 1 for nesting)", "3": "+3 (incl 2 for nesting)", "4": "+3 (incl 2 for nesting)", "5": "+2 (incl 1 for nesting)", "6": "+3 (incl 2 for nesting)", "7": "+1", "8": "+2 (incl 1 for nesting)", "9": "+1"}}}, {"ruleId": "CA1845", "level": "note", "message": "Use span-based 'string.Concat' and 'AsSpan' instead of 'Substring'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 414, "startColumn": 28, "endLine": 416, "endColumn": 54}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1845", "level": "note", "message": "Use span-based 'string.Concat' and 'AsSpan' instead of 'Substring'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 420, "startColumn": 28, "endLine": 422, "endColumn": 54}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadExisting()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 221, "startColumn": 9, "endLine": 221, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadByte()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 251, "startColumn": 43, "endLine": 251, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadByte()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 236, "startColumn": 44, "endLine": 236, "endColumn": 66}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardInBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 220, "startColumn": 9, "endLine": 220, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'romaNameArea'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 442, "startColumn": 16, "endLine": 442, "endColumn": 49}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadTimeout' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 223, "startColumn": 9, "endLine": 223, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 20 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 515, "startColumn": 19, "endLine": 515, "endColumn": 45}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 522, "startColumn": 9, "endLine": 522, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 530, "startColumn": 9, "endLine": 530, "endColumn": 14}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 532, "startColumn": 13, "endLine": 532, "endColumn": 16}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 534, "startColumn": 17, "endLine": 534, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 538, "startColumn": 17, "endLine": 538, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 546, "startColumn": 13, "endLine": 546, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 549, "startColumn": 17, "endLine": 549, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 553, "startColumn": 17, "endLine": 553, "endColumn": 21}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 560, "startColumn": 13, "endLine": 560, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 571, "startColumn": 9, "endLine": 571, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 575, "startColumn": 9, "endLine": 575, "endColumn": 13}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+1", "10": "+1", "2": "+2 (incl 1 for nesting)", "3": "+3 (incl 2 for nesting)", "4": "+3 (incl 2 for nesting)", "5": "+2 (incl 1 for nesting)", "6": "+3 (incl 2 for nesting)", "7": "+1", "8": "+2 (incl 1 for nesting)", "9": "+1"}}}, {"ruleId": "S3626", "level": "warning", "message": "Remove this redundant jump.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 454, "startColumn": 21, "endLine": 454, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1643", "level": "warning", "message": "Use a StringBuilder instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 466, "startColumn": 21, "endLine": 466, "endColumn": 46}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1643", "level": "warning", "message": "Use a StringBuilder instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 488, "startColumn": 17, "endLine": 488, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 472, "startColumn": 34, "endLine": 472, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 474, "startColumn": 42, "endLine": 474, "endColumn": 47}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 587, "startColumn": 34, "endLine": 587, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardOutBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 450, "startColumn": 21, "endLine": 450, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Write(string)' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 421, "startColumn": 25, "endLine": 421, "endColumn": 85}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadChar()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 415, "startColumn": 43, "endLine": 415, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardInBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 404, "startColumn": 13, "endLine": 404, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DataBits' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 400, "startColumn": 13, "endLine": 400, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadTimeout' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 436, "startColumn": 25, "endLine": 436, "endColumn": 48}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardOutBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 432, "startColumn": 25, "endLine": 432, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.BaudRate' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 399, "startColumn": 13, "endLine": 399, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Write(byte[], int, int)' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 434, "startColumn": 25, "endLine": 434, "endColumn": 72}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.DiscardOutBuffer()' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 405, "startColumn": 13, "endLine": 405, "endColumn": 43}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.ReadTimeout' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 397, "startColumn": 13, "endLine": 397, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Write(byte[], int, int)' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 452, "startColumn": 21, "endLine": 452, "endColumn": 68}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Write(byte[], int, int)' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 409, "startColumn": 13, "endLine": 409, "endColumn": 60}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.Parity' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 402, "startColumn": 13, "endLine": 402, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'iOS' 15.0 and later, 'maccatalyst' 15.0 and later. 'SerialPort.StopBits' is unsupported on: 'ios' all versions.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonSerialConnect.cs", "region": {"startLine": 401, "startColumn": 13, "endLine": 401, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1643", "level": "warning", "message": "Use a StringBuilder instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 540, "startColumn": 21, "endLine": 540, "endColumn": 46}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1643", "level": "warning", "message": "Use a StringBuilder instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 562, "startColumn": 17, "endLine": 562, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 546, "startColumn": 34, "endLine": 546, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 548, "startColumn": 42, "endLine": 548, "endColumn": 47}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2249", "level": "note", "message": "Use 'string.Contains' instead of 'string.IndexOf' to improve readability", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 546, "startColumn": 17, "endLine": 546, "endColumn": 45}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2249", "level": "note", "message": "Use 'string.Contains' instead of 'string.IndexOf' to improve readability", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.InterfaceLibrary/CommonCreateAttribute.cs", "region": {"startLine": 472, "startColumn": 17, "endLine": 472, "endColumn": 45}}}], "properties": {"warningLevel": 1}}], "rules": {"CA1416": {"id": "CA1416", "shortDescription": "Validate platform compatibility", "fullDescription": "Using platform dependent API on a component makes the code no longer work across all platforms.", "defaultLevel": "warning", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1416", "properties": {"category": "Interoperability", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1822": {"id": "CA1822", "shortDescription": "Mark members as static", "fullDescription": "Members that do not access instance data or call instance methods can be marked as static. After you mark the methods as static, the compiler will emit nonvirtual call sites to these members. This can give you a measurable performance gain for performance-sensitive code.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1822", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1845": {"id": "CA1845", "shortDescription": "Use span-based 'string.Concat'", "fullDescription": "It is more efficient to use 'AsSpan' and 'string.Concat', instead of 'Substring' and a concatenation operator.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1845", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1866": {"id": "CA1866", "shortDescription": "Use char overload", "fullDescription": "The char overload is a better performing overload than a string with a single char.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1866", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2200": {"id": "CA2200", "shortDescription": "Rethrow to preserve stack details", "defaultLevel": "warning", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2200", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2249": {"id": "CA2249", "shortDescription": "Consider using 'string.Contains' instead of 'string.IndexOf'", "fullDescription": "Calls to 'string.IndexOf' where the result is used to check for the presence/absence of a substring can be replaced by 'string.Contains'.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2249", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CS8600": {"id": "CS8600", "shortDescription": "Converting null literal or possible null value to non-nullable type.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8600)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8602": {"id": "CS8602", "shortDescription": "Dereference of a possibly null reference.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8602)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8603": {"id": "CS8603", "shortDescription": "Possible null reference return.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8603)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8605": {"id": "CS8605", "shortDescription": "Unboxing a possibly null value.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8605)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8618": {"id": "CS8618", "shortDescription": "Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8618)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8625": {"id": "CS8625", "shortDescription": "Cannot convert null literal to non-nullable reference type.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8625)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "S1066": {"id": "S1066", "shortDescription": "Mergeable \"if\" statements should be combined", "fullDescription": "Nested code - blocks of code inside blocks of code - is eventually necessary, but increases complexity. This is why keeping the code as flat as possible, by avoiding unnecessary nesting, is considered a good practice.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1066", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S108": {"id": "S108", "shortDescription": "Nested blocks of code should not be left empty", "fullDescription": "An empty code block is confusing. It will require some effort from maintainers to determine if it is intentional or indicates the implementation is incomplete.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-108", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1104": {"id": "S1104", "shortDescription": "Fields should not have public accessibility", "fullDescription": "Public fields in public classes do not respect the encapsulation principle and have three main disadvantages:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1104", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S1118": {"id": "S1118", "shortDescription": "Utility classes should not have public constructors", "fullDescription": "Whenever there are portions of code that are duplicated and do not depend on the state of their container class, they can be centralized inside a \"utility class\". A utility class is a class that only has static members, hence it should not be instantiated.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1118", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1125": {"id": "S1125", "shortDescription": "Boolean literals should not be redundant", "fullDescription": "A boolean literal can be represented in two different ways: true or false. They can be combined with logical operators (!, &&, ||, ==, !=) to produce logical expressions that represent truth values. However, comparing a boolean literal to a variable or expression that evaluates to a boolean value is unnecessary and can make the code harder to read and understand. The more complex a boolean expression is, the harder it will be for developers to understand its meaning and expected behavior, and it will favour the introduction of new bugs.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1125", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1192": {"id": "S1192", "shortDescription": "String literals should not be duplicated", "fullDescription": "Duplicated string literals make the process of refactoring complex and error-prone, as any change would need to be propagated on all occurrences.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1192", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": false, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S125": {"id": "S125", "shortDescription": "Sections of code should not be commented out", "fullDescription": "Commented-out code distracts the focus from the actual executed code. It creates a noise that increases maintenance code. And because it is never executed, it quickly becomes out of date and invalid.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-125", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1481": {"id": "S1481", "shortDescription": "Unused local variables should be removed", "fullDescription": "An unused local variable is a variable that has been declared but is not used anywhere in the block of code where it is defined. It is dead code, contributing to unnecessary complexity and leading to confusion when reading the code. Therefore, it should be removed from your code to maintain clarity and efficiency.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1481", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1643": {"id": "S1643", "shortDescription": "Strings should not be concatenated using '+' in a loop", "fullDescription": "Concatenating multiple string literals or strings using the + operator creates a new string object for each concatenation. This can lead to a large number of intermediate string objects and can be inefficient. The StringBuilder class is more efficient than string concatenation, especially when the operator is repeated over and over as in loops.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1643", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S1854": {"id": "S1854", "shortDescription": "Unused assignments should be removed", "fullDescription": "Dead stores refer to assignments made to local variables that are subsequently never used or immediately overwritten. Such assignments are unnecessary and don’t contribute to the functionality or clarity of the code. They may even negatively impact performance. Removing them enhances code cleanliness and readability. Even if the unnecessary operations do not do any harm in terms of the program’s correctness, they are - at best - a waste of computing resources.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1854", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2292": {"id": "S2292", "shortDescription": "Trivial properties should be auto-implemented", "fullDescription": "Trivial properties, which include no logic but setting and getting a backing field should be converted to auto-implemented properties, yielding cleaner and more readable code.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2292", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2325": {"id": "S2325", "shortDescription": "Methods and properties that don't access instance data should be static", "fullDescription": "Methods and properties that don’t access instance data should be marked as static for the following reasons:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2325", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2486": {"id": "S2486", "shortDescription": "Generic exceptions should not be ignored", "fullDescription": "When exceptions occur, it is usually a bad idea to simply ignore them. Instead, it is better to handle them properly, or at least to log them.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2486", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2933": {"id": "S2933", "shortDescription": "Fields that are only assigned in the constructor should be \"readonly\"", "fullDescription": "readonly fields can only be assigned in a class constructor. If a class has a field that’s not marked readonly but is only set in the constructor, it could cause confusion about the field’s intended use. To avoid confusion, such fields should be marked readonly to make their intended use explicit, and to prevent future maintainers from inadvertently changing their use.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2933", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3442": {"id": "S3442", "shortDescription": "\"abstract\" classes should not have \"public\" constructors", "fullDescription": "The abstract modifier in a class declaration is used to indicate that a class is intended only to be a base class of other classes, not instantiated on its own.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3442", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3445": {"id": "S3445", "shortDescription": "Exceptions should not be explicitly rethrown", "fullDescription": "In C#, the throw statement can be used in two different ways:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3445", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S3626": {"id": "S3626", "shortDescription": "Jump statements should not be redundant", "fullDescription": "Jump statements, such as return, yield break, goto, and continue let you change the default flow of program execution, but jump statements that direct the control flow to the original direction are just a waste of keystrokes.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3626", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3776": {"id": "S3776", "shortDescription": "Cognitive Complexity of methods should not be too high", "fullDescription": "This rule raises an issue when the code cognitive complexity of a function is above a certain threshold.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3776", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": false, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S4136": {"id": "S4136", "shortDescription": "Method overloads should be grouped together", "fullDescription": "For clarity, all overloads of the same method should be grouped together. That lets both users and maintainers quickly understand all the current available options.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4136", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S4663": {"id": "S4663", "shortDescription": "Comments should not be empty", "fullDescription": "Empty comments, as shown in the example, hurt readability and might indicate an oversight.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4663", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}}}]}