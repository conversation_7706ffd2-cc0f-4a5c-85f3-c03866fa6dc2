{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/IAppNavigator.cs", "region": {"startLine": 7, "startColumn": 59, "endLine": 7, "endColumn": 66}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/IAppNavigator.cs", "region": {"startLine": 9, "startColumn": 76, "endLine": 9, "endColumn": 83}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/IAppNavigator.cs", "region": {"startLine": 13, "startColumn": 49, "endLine": 13, "endColumn": 56}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/IAppNavigator.cs", "region": {"startLine": 15, "startColumn": 64, "endLine": 15, "endColumn": 71}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/IAppNavigator.cs", "region": {"startLine": 16, "startColumn": 70, "endLine": 16, "endColumn": 77}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 17, "startColumn": 66, "endLine": 17, "endColumn": 73}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 22, "startColumn": 83, "endLine": 22, "endColumn": 90}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 56, "startColumn": 56, "endLine": 56, "endColumn": 63}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 63, "startColumn": 77, "endLine": 63, "endColumn": 84}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 69, "startColumn": 83, "endLine": 69, "endColumn": 90}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/UriHelper.cs", "region": {"startLine": 19, "startColumn": 35, "endLine": 19, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/UriHelper.cs", "region": {"startLine": 25, "startColumn": 16, "endLine": 25, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'data' in 'Task IAppNavigator.GoBackAsync(bool animated = false, object data = null)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/MVVM/BaseViewModel.cs", "region": {"startLine": 30, "startColumn": 74, "endLine": 30, "endColumn": 92}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS0618", "level": "warning", "message": "'__KnownINotifyPropertyChangingArgs' is obsolete: 'This type is not intended to be used directly by user code'", "suppressionStates": ["suppressedInSource"], "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/obj/Debug/net9.0-android/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/ShinKenShinKun.CoreMVVM.BaseViewModel.g.cs", "region": {"startLine": 21, "startColumn": 40, "endLine": 21, "endColumn": 131}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS0618", "level": "warning", "message": "'__KnownINotifyPropertyChangingArgs.IsBusy' is obsolete: 'This field is not intended to be referenced directly by user code'", "suppressionStates": ["suppressedInSource"], "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/obj/Debug/net9.0-android/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/ShinKenShinKun.CoreMVVM.BaseViewModel.g.cs", "region": {"startLine": 21, "startColumn": 40, "endLine": 21, "endColumn": 138}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS0618", "level": "warning", "message": "'__KnownINotifyPropertyChangedArgs' is obsolete: 'This type is not intended to be used directly by user code'", "suppressionStates": ["suppressedInSource"], "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/obj/Debug/net9.0-android/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/ShinKenShinKun.CoreMVVM.BaseViewModel.g.cs", "region": {"startLine": 25, "startColumn": 39, "endLine": 25, "endColumn": 129}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS0618", "level": "warning", "message": "'__KnownINotifyPropertyChangedArgs.IsBusy' is obsolete: 'This field is not intended to be referenced directly by user code'", "suppressionStates": ["suppressedInSource"], "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/obj/Debug/net9.0-android/CommunityToolkit.Mvvm.SourceGenerators/CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator/ShinKenShinKun.CoreMVVM.BaseViewModel.g.cs", "region": {"startLine": 25, "startColumn": 39, "endLine": 25, "endColumn": 136}}}], "properties": {"warningLevel": 2}}, {"ruleId": "S4487", "level": "warning", "message": "Remove this unread private field 'serviceProvider' or refactor the code to use its value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 9, "startColumn": 39, "endLine": 9, "endColumn": 54}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6668", "level": "warning", "message": "Logging arguments should be passed to the correct parameter.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 47, "startColumn": 13, "endLine": 47, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/AppNavigator.cs", "region": {"startLine": 47, "startColumn": 51, "endLine": 47, "endColumn": 62}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null}}}, {"ruleId": "S2629", "level": "warning", "message": "Don't use string interpolation in logging message templates.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/NavigationAwareBaseViewModel.cs", "region": {"startLine": 13, "startColumn": 29, "endLine": 13, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2629", "level": "warning", "message": "Don't use string interpolation in logging message templates.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/Navigation/NavigationAwareBaseViewModel.cs", "region": {"startLine": 18, "startColumn": 29, "endLine": 18, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1825", "level": "note", "message": "Avoid unnecessary zero-length array allocations.  Use Array.Empty<string>() instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/MVVM/BaseFormModel.cs", "region": {"startLine": 5, "startColumn": 70, "endLine": 5, "endColumn": 83}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2629", "level": "warning", "message": "Don't use string interpolation in logging message templates.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/MVVM/BaseViewModel.cs", "region": {"startLine": 24, "startColumn": 25, "endLine": 24, "endColumn": 74}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2629", "level": "warning", "message": "Don't use string interpolation in logging message templates.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.CoreMVVM/MVVM/BaseViewModel.cs", "region": {"startLine": 17, "startColumn": 25, "endLine": 17, "endColumn": 71}}}], "properties": {"warningLevel": 1}}], "rules": {"CA1825": {"id": "CA1825", "shortDescription": "Avoid zero-length array allocations", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1825", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CS0618": {"id": "CS0618", "shortDescription": "Type or member is obsolete", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS0618)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8603": {"id": "CS8603", "shortDescription": "Possible null reference return.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8603)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8604": {"id": "CS8604", "shortDescription": "Possible null reference argument.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8604)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8625": {"id": "CS8625", "shortDescription": "Cannot convert null literal to non-nullable reference type.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8625)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "S2629": {"id": "S2629", "shortDescription": "Logging templates should be constant", "fullDescription": "Logging arguments should not require evaluation in order to avoid unnecessary performance overhead. When passing concatenated strings or string interpolations directly into a logging method, the evaluation of these expressions occurs every time the logging method is called, regardless of the log level. This can lead to inefficient code execution and increased resource consumption.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2629", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S4487": {"id": "S4487", "shortDescription": "Unread \"private\" fields should be removed", "fullDescription": "Private fields which are written but never read are a case of \"dead store\". Changing the value of such a field is useless and most probably indicates an error in the code.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4487", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S6668": {"id": "S6668", "shortDescription": "Logging arguments should be passed to the correct parameter", "fullDescription": "Most logging frameworks have methods that take a log level, an event ID or an exception as a separate input next to the log format and its arguments. There is a high chance that if the log level, the event ID or the exception are passed as the arguments to the message format, it was a mistake. This rule is going to raise in that scenario.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-6668", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}}}]}