{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "CS8618", "level": "warning", "message": "Non-nullable property 'Item' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoItems/Commands/UpdateTodoItem/UpdateTodoItemCommand.cs", "region": {"startLine": 5, "startColumn": 24, "endLine": 5, "endColumn": 28}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoItems/Commands/UpdateTodoItem/UpdateTodoItemCommand.cs", "region": {"startLine": 5, "startColumn": 24, "endLine": 5, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable property 'Summary' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoLists/Commands/UpdateTodoList/UpdateTodoListCommand.cs", "region": {"startLine": 5, "startColumn": 31, "endLine": 5, "endColumn": 38}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoLists/Commands/UpdateTodoList/UpdateTodoListCommand.cs", "region": {"startLine": 5, "startColumn": 31, "endLine": 5, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable property 'Item' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoItems/Commands/UpdateTodoItemDetail/UpdateTodoItemDetailCommand.cs", "region": {"startLine": 5, "startColumn": 24, "endLine": 5, "endColumn": 28}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoItems/Commands/UpdateTodoItemDetail/UpdateTodoItemDetailCommand.cs", "region": {"startLine": 5, "startColumn": 24, "endLine": 5, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable property 'Item' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoItems/Commands/CreateTodoItem/CreateTodoItemCommand.cs", "region": {"startLine": 5, "startColumn": 27, "endLine": 5, "endColumn": 31}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoItems/Commands/CreateTodoItem/CreateTodoItemCommand.cs", "region": {"startLine": 5, "startColumn": 27, "endLine": 5, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Mappings/MappingExtensions.cs", "region": {"startLine": 6, "startColumn": 5, "endLine": 6, "endColumn": 116}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2245", "level": "warning", "message": "Make sure that using this pseudorandom number generator is safe here.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/WeatherForecasts/Queries/GetWeatherForecasts/GetWeatherForecastsQuery.cs", "region": {"startLine": 14, "startColumn": 19, "endLine": 14, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2263", "level": "note", "message": "Prefer the generic overload 'System.Enum.GetValues<TEnum>()' instead of 'System.Enum.GetValues(System.Type)'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/TodoLists/Queries/GetTodos/GetTodosQuery.cs", "region": {"startLine": 21, "startColumn": 30, "endLine": 21, "endColumn": 67}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6672", "level": "warning", "message": "Update this logger to use its enclosing type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/UnhandledExceptionBehaviour.cs", "region": {"startLine": 7, "startColumn": 48, "endLine": 7, "endColumn": 56}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2139", "level": "warning", "message": "Either log this exception and handle it, or rethrow it with some contextual information.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/UnhandledExceptionBehaviour.cs", "region": {"startLine": 18, "startColumn": 26, "endLine": 18, "endColumn": 28}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/UnhandledExceptionBehaviour.cs", "region": {"startLine": 22, "startColumn": 13, "endLine": 22, "endColumn": 132}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/UnhandledExceptionBehaviour.cs", "region": {"startLine": 24, "startColumn": 13, "endLine": 24, "endColumn": 19}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Logging statement.", "1": "Thrown exception."}}}, {"ruleId": "S6672", "level": "warning", "message": "Update this logger to use its enclosing type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/PerformanceBehaviour.cs", "region": {"startLine": 11, "startColumn": 17, "endLine": 11, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1860", "level": "note", "message": "Prefer comparing 'Count' to 0 rather than using 'Any()', both for clarity and for performance", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/ValidationBehaviour.cs", "region": {"startLine": 24, "startColumn": 29, "endLine": 24, "endColumn": 43}}}], "properties": {"warningLevel": 1, "customProperties": {"DiagnosticPropertyKey": "Count"}}}, {"ruleId": "CA1860", "level": "note", "message": "Prefer comparing 'Count' to 0 rather than using 'Any()', both for clarity and for performance", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/ValidationBehaviour.cs", "region": {"startLine": 28, "startColumn": 17, "endLine": 28, "endColumn": 31}}}], "properties": {"warningLevel": 1, "customProperties": {"DiagnosticPropertyKey": "Count"}}}, {"ruleId": "S6672", "level": "warning", "message": "Update this logger to use its enclosing type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/LoggingBehaviour.cs", "region": {"startLine": 9, "startColumn": 37, "endLine": 9, "endColumn": 45}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 29 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 16, "startColumn": 34, "endLine": 16, "endColumn": 40}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 20, "startColumn": 9, "endLine": 20, "endColumn": 11}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 23, "startColumn": 13, "endLine": 23, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 31, "startColumn": 13, "endLine": 31, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 35, "startColumn": 17, "endLine": 35, "endColumn": 24}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 37, "startColumn": 21, "endLine": 37, "endColumn": 28}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 40, "startColumn": 25, "endLine": 40, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 49, "startColumn": 17, "endLine": 49, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 57, "startColumn": 13, "endLine": 57, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 59, "startColumn": 17, "endLine": 59, "endColumn": 24}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Application/Common/Behaviours/AuthorizationBehaviour.cs", "region": {"startLine": 63, "startColumn": 21, "endLine": 63, "endColumn": 23}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+2 (incl 1 for nesting)", "2": "+2 (incl 1 for nesting)", "3": "+3 (incl 2 for nesting)", "4": "+4 (incl 3 for nesting)", "5": "+5 (incl 4 for nesting)", "6": "+3 (incl 2 for nesting)", "7": "+2 (incl 1 for nesting)", "8": "+3 (incl 2 for nesting)", "9": "+4 (incl 3 for nesting)"}}}], "rules": {"CA1860": {"id": "CA1860", "shortDescription": "Avoid using 'Enumerable.Any()' extension method", "fullDescription": "Prefer using 'IsEmpty', 'Count' or 'Length' properties whichever available, rather than calling 'Enumerable.Any()'. The intent is clearer and it is more performant than using 'Enumerable.Any()' extension method.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1860", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2263": {"id": "CA2263", "shortDescription": "Prefer generic overload when type is known", "fullDescription": "Using a generic overload is preferable to the 'System.Type' overload when the type is known, promoting cleaner and more type-safe code with improved compile-time checks.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2263", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CS8618": {"id": "CS8618", "shortDescription": "Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8618)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "S125": {"id": "S125", "shortDescription": "Sections of code should not be commented out", "fullDescription": "Commented-out code distracts the focus from the actual executed code. It creates a noise that increases maintenance code. And because it is never executed, it quickly becomes out of date and invalid.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-125", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2139": {"id": "S2139", "shortDescription": "Exceptions should be either logged or rethrown but not both", "fullDescription": "When an exception is logged and rethrown, the upstream code may not be aware that the exception has already been logged. As a result, the same exception gets logged multiple times, making it difficult to identify the root cause of the issue. This can be particularly problematic in multi-threaded applications where messages from other threads can be interwoven with the repeated log entries.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2139", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2245": {"id": "S2245", "shortDescription": "Using pseudorandom number generators (PRNGs) is security-sensitive", "fullDescription": "PRNGs are algorithms that produce sequences of numbers that only approximate true randomness. While they are suitable for applications like simulations or modeling, they are not appropriate for security-sensitive contexts because their outputs can be predictable if the internal state is known.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2245", "properties": {"category": "Critical Security Hotspot", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S3776": {"id": "S3776", "shortDescription": "Cognitive Complexity of methods should not be too high", "fullDescription": "This rule raises an issue when the code cognitive complexity of a function is above a certain threshold.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3776", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": false, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S6672": {"id": "S6672", "shortDescription": "Generic logger injection should match enclosing type", "fullDescription": "In most logging frameworks, it’s good practice to set the logger name to match its enclosing type, as enforced by {rule:csharpsquid:S3416}.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-6672", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}}}]}