<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>ShinKenShinKun.Utils</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>4a5fa1ec-75cb-418c-a840-02c4454f61ce</ProjectGuid>
  <FullPath>C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun.Utils\ShinKenShinKun.Utils.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Data\git\arcteckenshinkunvup2024\.sonarqube\conf\104\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\104\Issues.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\104</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
  <TargetFramework>net9.0-android</TargetFramework>
</ProjectInfo>