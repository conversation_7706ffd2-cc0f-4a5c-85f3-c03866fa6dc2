{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Domain/Entities/TodoItem.cs", "region": {"startLine": 21, "startColumn": 23, "endLine": 21, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1125", "level": "warning", "message": "Remove the unnecessary Boolean literal(s).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Core/Domain/Entities/TodoItem.cs", "region": {"startLine": 21, "startColumn": 40, "endLine": 21, "endColumn": 48}}}], "properties": {"warningLevel": 1}}], "rules": {"S1125": {"id": "S1125", "shortDescription": "Boolean literals should not be redundant", "fullDescription": "A boolean literal can be represented in two different ways: true or false. They can be combined with logical operators (!, &&, ||, ==, !=) to produce logical expressions that represent truth values. However, comparing a boolean literal to a variable or expression that evaluates to a boolean value is unnecessary and can make the code harder to read and understand. The more complex a boolean expression is, the harder it will be for developers to understand its meaning and expected behavior, and it will favour the introduction of new bugs.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1125", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}}}]}