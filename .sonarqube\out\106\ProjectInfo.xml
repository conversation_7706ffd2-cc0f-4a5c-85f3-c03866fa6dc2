<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>Infrastructure</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>3d745dfe-af9a-e111-755d-fd3fff280728</ProjectGuid>
  <FullPath>C:\Data\git\arcteckenshinkunvup2024\src\Infrastructure\Infrastructure.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Data\git\arcteckenshinkunvup2024\.sonarqube\conf\106\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\106\Issues.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\106</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
  <TargetFramework>net9.0</TargetFramework>
</ProjectInfo>