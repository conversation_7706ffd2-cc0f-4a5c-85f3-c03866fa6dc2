<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>ShinKenShinKun.CoreMVVM</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>630ee80f-4784-4e95-b6a9-9206c0657999</ProjectGuid>
  <FullPath>C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun.CoreMVVM\ShinKenShinKun.CoreMVVM.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Data\git\arcteckenshinkunvup2024\.sonarqube\conf\108\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\108\Issues.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\108</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
  <TargetFramework>net9.0-ios</TargetFramework>
</ProjectInfo>