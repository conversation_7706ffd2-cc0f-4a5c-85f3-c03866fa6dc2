<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>ShinKenShinKun.DataAccess</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>c7a81bf5-abf7-49a8-a9c9-735ef0ae5d53</ProjectGuid>
  <FullPath>C:\Data\git\arcteckenshinkunvup2024\src\Presentation\UI\App\ShinKenShinKun.DataAccess\ShinKenShinKun.DataAccess.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Data\git\arcteckenshinkunvup2024\.sonarqube\conf\118\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\118\Issues.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\118</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
  <TargetFramework>net9.0-windows10.0.19041.0</TargetFramework>
</ProjectInfo>