{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'x' of 'int BackUpComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpComparer.cs", "region": {"startLine": 16, "startColumn": 16, "endLine": 16, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'y' of 'int BackUpComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpComparer.cs", "region": {"startLine": 16, "startColumn": 16, "endLine": 16, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'x' of 'int BackUpFileControlComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpFileControlComparer.cs", "region": {"startLine": 32, "startColumn": 16, "endLine": 32, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'y' of 'int BackUpFileControlComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpFileControlComparer.cs", "region": {"startLine": 32, "startColumn": 16, "endLine": 32, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'x' of 'int BackUpFileControlComparer.Compare(string x, string y)' doesn't match implicitly implemented member 'int IComparer<string>.Compare(string? x, string? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpFileControlComparer.cs", "region": {"startLine": 17, "startColumn": 16, "endLine": 17, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'y' of 'int BackUpFileControlComparer.Compare(string x, string y)' doesn't match implicitly implemented member 'int IComparer<string>.Compare(string? x, string? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpFileControlComparer.cs", "region": {"startLine": 17, "startColumn": 16, "endLine": 17, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'x' of 'int ListViewItemComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 79, "startColumn": 16, "endLine": 79, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'y' of 'int ListViewItemComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 79, "startColumn": 16, "endLine": 79, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'x' of 'int MyComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/MyComparer.cs", "region": {"startLine": 10, "startColumn": 16, "endLine": 10, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'y' of 'int MyComparer.Compare(object x, object y)' doesn't match implicitly implemented member 'int IComparer.Compare(object? x, object? y)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/MyComparer.cs", "region": {"startLine": 10, "startColumn": 16, "endLine": 10, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'value' of 'object DateTimeToStringConverter.Convert(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.Convert(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/DateTimeToStringConverter.cs", "region": {"startLine": 5, "startColumn": 19, "endLine": 5, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'parameter' of 'object DateTimeToStringConverter.Convert(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.Convert(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/DateTimeToStringConverter.cs", "region": {"startLine": 5, "startColumn": 19, "endLine": 5, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'value' of 'object DateTimeToStringConverter.ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/DateTimeToStringConverter.cs", "region": {"startLine": 16, "startColumn": 19, "endLine": 16, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'parameter' of 'object DateTimeToStringConverter.ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/DateTimeToStringConverter.cs", "region": {"startLine": 16, "startColumn": 19, "endLine": 16, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'value' of 'object FontSizeByTextLengthConverter.Convert(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.Convert(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/FontSizeByTextLengthConverter.cs", "region": {"startLine": 5, "startColumn": 19, "endLine": 5, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'parameter' of 'object FontSizeByTextLengthConverter.Convert(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.Convert(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/FontSizeByTextLengthConverter.cs", "region": {"startLine": 5, "startColumn": 19, "endLine": 5, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'value' of 'object FontSizeByTextLengthConverter.ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/FontSizeByTextLengthConverter.cs", "region": {"startLine": 15, "startColumn": 19, "endLine": 15, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'parameter' of 'object FontSizeByTextLengthConverter.ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/FontSizeByTextLengthConverter.cs", "region": {"startLine": 15, "startColumn": 19, "endLine": 15, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'value' of 'object ImageConverter.Convert(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.Convert(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ImageConverter.cs", "region": {"startLine": 5, "startColumn": 19, "endLine": 5, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'parameter' of 'object ImageConverter.Convert(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.Convert(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ImageConverter.cs", "region": {"startLine": 5, "startColumn": 19, "endLine": 5, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'value' of 'object ImageConverter.ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ImageConverter.cs", "region": {"startLine": 20, "startColumn": 19, "endLine": 20, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8767", "level": "warning", "message": "Nullability of reference types in type of parameter 'parameter' of 'object ImageConverter.ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)' doesn't match implicitly implemented member 'object? IValueConverter.ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)' (possibly because of nullability attributes).", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ImageConverter.cs", "region": {"startLine": 20, "startColumn": 19, "endLine": 20, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpComparer.cs", "region": {"startLine": 21, "startColumn": 43, "endLine": 21, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpComparer.cs", "region": {"startLine": 22, "startColumn": 43, "endLine": 22, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field '_columnModes' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 63, "startColumn": 12, "endLine": 63, "endColumn": 32}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 15, "startColumn": 28, "endLine": 15, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field '_columnModes' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 69, "startColumn": 12, "endLine": 69, "endColumn": 32}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 15, "startColumn": 28, "endLine": 15, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/MyComparer.cs", "region": {"startLine": 15, "startColumn": 43, "endLine": 15, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/MyComparer.cs", "region": {"startLine": 16, "startColumn": 43, "endLine": 16, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/FontSizeByTextLengthConverter.cs", "region": {"startLine": 7, "startColumn": 25, "endLine": 7, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/FontSizeByTextLengthConverter.cs", "region": {"startLine": 8, "startColumn": 13, "endLine": 8, "endColumn": 19}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ImageConverter.cs", "region": {"startLine": 17, "startColumn": 16, "endLine": 17, "endColumn": 20}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS0162", "level": "warning", "message": "Unreachable code detected", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 14, "startColumn": 9, "endLine": 14, "endColumn": 12}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 7, "startColumn": 16, "endLine": 7, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 12, "startColumn": 9, "endLine": 12, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 17, "startColumn": 9, "endLine": 17, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8625", "level": "warning", "message": "Cannot convert null literal to non-nullable reference type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 17, "startColumn": 60, "endLine": 17, "endColumn": 64}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 19, "startColumn": 62, "endLine": 19, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 19, "startColumn": 75, "endLine": 19, "endColumn": 108}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 19, "startColumn": 20, "endLine": 19, "endColumn": 115}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 23, "startColumn": 60, "endLine": 23, "endColumn": 63}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 23, "startColumn": 73, "endLine": 23, "endColumn": 106}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 23, "startColumn": 20, "endLine": 23, "endColumn": 113}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 43, "startColumn": 60, "endLine": 43, "endColumn": 63}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 43, "startColumn": 73, "endLine": 43, "endColumn": 106}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 43, "startColumn": 20, "endLine": 43, "endColumn": 113}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 47, "startColumn": 62, "endLine": 47, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 47, "startColumn": 75, "endLine": 47, "endColumn": 108}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 47, "startColumn": 20, "endLine": 47, "endColumn": 115}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 77, "startColumn": 28, "endLine": 77, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB0044", "level": "warning", "message": "'AssemblyName.CodeBase' is obsolete: 'AssemblyName.CodeBase and AssemblyName.EscapedCodeBase are obsolete. Using them for loading an assembly is not supported.'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 37, "startColumn": 33, "endLine": 37, "endColumn": 79}}}], "properties": {"warningLevel": 2}}, {"ruleId": "SYSLIB0044", "level": "warning", "message": "'AssemblyName.CodeBase' is obsolete: 'AssemblyName.CodeBase and AssemblyName.EscapedCodeBase are obsolete. Using them for loading an assembly is not supported.'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 43, "startColumn": 34, "endLine": 43, "endColumn": 80}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 37, "startColumn": 33, "endLine": 37, "endColumn": 60}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 37, "startColumn": 33, "endLine": 37, "endColumn": 79}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 43, "startColumn": 34, "endLine": 43, "endColumn": 61}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'path2' in 'string Path.Combine(string path1, string path2)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 43, "startColumn": 17, "endLine": 43, "endColumn": 81}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 45, "startColumn": 26, "endLine": 45, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 140, "startColumn": 25, "endLine": 140, "endColumn": 115}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 145, "startColumn": 25, "endLine": 145, "endColumn": 117}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB0044", "level": "warning", "message": "'AssemblyName.CodeBase' is obsolete: 'AssemblyName.CodeBase and AssemblyName.EscapedCodeBase are obsolete. Using them for loading an assembly is not supported.'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/DirUtility.cs", "region": {"startLine": 28, "startColumn": 57, "endLine": 28, "endColumn": 107}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/DirUtility.cs", "region": {"startLine": 28, "startColumn": 35, "endLine": 28, "endColumn": 108}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/DirUtility.cs", "region": {"startLine": 34, "startColumn": 16, "endLine": 34, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS0219", "level": "warning", "message": "The variable 'r' is assigned but its value is never used", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 114, "startColumn": 33, "endLine": 114, "endColumn": 34}}}], "properties": {"warningLevel": 3}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 167, "startColumn": 24, "endLine": 167, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 173, "startColumn": 20, "endLine": 173, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 's' in 'int int.Parse(string s)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 17, "startColumn": 34, "endLine": 17, "endColumn": 49}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 's' in 'double double.Parse(string s)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 40, "startColumn": 37, "endLine": 40, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/PropertyHelpers.cs", "region": {"startLine": 8, "startColumn": 35, "endLine": 8, "endColumn": 60}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/PropertyHelpers.cs", "region": {"startLine": 8, "startColumn": 16, "endLine": 8, "endColumn": 70}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 's' in 'decimal decimal.Parse(string s)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 63, "startColumn": 38, "endLine": 63, "endColumn": 53}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 's' in 'long long.Parse(string s)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 86, "startColumn": 35, "endLine": 86, "endColumn": 50}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 's' in 'int int.Parse(string s)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 110, "startColumn": 52, "endLine": 110, "endColumn": 67}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 126, "startColumn": 24, "endLine": 126, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 130, "startColumn": 16, "endLine": 130, "endColumn": 20}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'messageTemplate' in 'void Log.Error(string messageTemplate)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/PropertyHelpers.cs", "region": {"startLine": 51, "startColumn": 23, "endLine": 51, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ReflectionHelper.cs", "region": {"startLine": 8, "startColumn": 37, "endLine": 8, "endColumn": 67}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ReflectionHelper.cs", "region": {"startLine": 9, "startColumn": 24, "endLine": 9, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ReflectionHelper.cs", "region": {"startLine": 9, "startColumn": 24, "endLine": 9, "endColumn": 59}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ReflectionHelper.cs", "region": {"startLine": 10, "startColumn": 16, "endLine": 10, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ReflectionHelper.cs", "region": {"startLine": 16, "startColumn": 37, "endLine": 16, "endColumn": 67}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8602", "level": "warning", "message": "Dereference of a possibly null reference.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ReflectionHelper.cs", "region": {"startLine": 17, "startColumn": 9, "endLine": 17, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 55, "startColumn": 37, "endLine": 55, "endColumn": 75}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 68, "startColumn": 32, "endLine": 68, "endColumn": 68}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 74, "startColumn": 32, "endLine": 74, "endColumn": 68}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8629", "level": "warning", "message": "Nullable value type may be null.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 80, "startColumn": 32, "endLine": 80, "endColumn": 68}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 7, "startColumn": 25, "endLine": 7, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 70, "startColumn": 22, "endLine": 70, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 73, "startColumn": 16, "endLine": 73, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'DbCtl' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 20, "startColumn": 13, "endLine": 20, "endColumn": 25}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 11, "startColumn": 31, "endLine": 11, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'Logger' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 20, "startColumn": 13, "endLine": 20, "endColumn": 25}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 17, "startColumn": 30, "endLine": 17, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 17, "startColumn": 31, "endLine": 17, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8600", "level": "warning", "message": "Converting null literal or possible null value to non-nullable type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 38, "startColumn": 22, "endLine": 38, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 49, "startColumn": 16, "endLine": 49, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'path' in 'void FileOut.FileDelete(string path)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 25, "startColumn": 24, "endLine": 25, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'FileOutLocalPath' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 18, "startColumn": 13, "endLine": 18, "endColumn": 20}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 9, "startColumn": 20, "endLine": 9, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'Logger' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 18, "startColumn": 13, "endLine": 18, "endColumn": 20}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 11, "startColumn": 26, "endLine": 11, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'DbCtl' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 18, "startColumn": 13, "endLine": 18, "endColumn": 20}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 12, "startColumn": 31, "endLine": 12, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'data' in 'void ClientLogger.Write(string title, string data)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 92, "startColumn": 45, "endLine": 92, "endColumn": 57}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB0044", "level": "warning", "message": "'AssemblyName.CodeBase' is obsolete: 'AssemblyName.CodeBase and AssemblyName.EscapedCodeBase are obsolete. Using them for loading an assembly is not supported.'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 212, "startColumn": 57, "endLine": 212, "endColumn": 107}}}], "properties": {"warningLevel": 2}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'uriString' in 'Uri.Uri(string uriString)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 212, "startColumn": 35, "endLine": 212, "endColumn": 108}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS0168", "level": "warning", "message": "The variable 'er' is declared but never used", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 223, "startColumn": 34, "endLine": 223, "endColumn": 36}}}], "properties": {"warningLevel": 3}}, {"ruleId": "CS0168", "level": "warning", "message": "The variable 'ex' is declared but never used", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 256, "startColumn": 26, "endLine": 256, "endColumn": 28}}}], "properties": {"warningLevel": 3}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable field 'Instance' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 7, "startColumn": 28, "endLine": 7, "endColumn": 36}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 7, "startColumn": 28, "endLine": 7, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8604", "level": "warning", "message": "Possible null reference argument for parameter 'path' in 'void File.Delete(string path)'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 124, "startColumn": 45, "endLine": 124, "endColumn": 61}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 84, "startColumn": 9, "endLine": 84, "endColumn": 48}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/MyComparer.cs", "region": {"startLine": 6, "startColumn": 5, "endLine": 6, "endColumn": 52}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/MyComparer.cs", "region": {"startLine": 22, "startColumn": 9, "endLine": 22, "endColumn": 45}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1135", "level": "warning", "message": "Complete the task associated to this 'TODO' comment.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/AppConstants.cs", "region": {"startLine": 125, "startColumn": 7, "endLine": 125, "endColumn": 11}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/FormDispModeConst.cs", "region": {"startLine": 102, "startColumn": 5, "endLine": 102, "endColumn": 53}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ColorConverter.cs", "region": {"startLine": 6, "startColumn": 5, "endLine": 6, "endColumn": 8}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 21, "startColumn": 5, "endLine": 21, "endColumn": 8}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 34, "startColumn": 5, "endLine": 34, "endColumn": 8}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/UIElementExtension.cs", "region": {"startLine": 6, "startColumn": 9, "endLine": 6, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/UserControlExtension.cs", "region": {"startLine": 6, "startColumn": 5, "endLine": 6, "endColumn": 8}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 93, "startColumn": 13, "endLine": 93, "endColumn": 58}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 149, "startColumn": 9, "endLine": 149, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 36, "startColumn": 9, "endLine": 36, "endColumn": 102}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 41, "startColumn": 13, "endLine": 41, "endColumn": 105}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 109, "startColumn": 17, "endLine": 109, "endColumn": 54}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 144, "startColumn": 9, "endLine": 144, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 207, "startColumn": 9, "endLine": 207, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 8, "startColumn": 5, "endLine": 8, "endColumn": 63}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3260", "level": "warning", "message": "Private classes which are not derived in the current assembly should be marked as 'sealed'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 95, "startColumn": 19, "endLine": 95, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 24, "startColumn": 13, "endLine": 24, "endColumn": 60}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/CheckSound.cs", "region": {"startLine": 63, "startColumn": 9, "endLine": 63, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/CheckSound.cs", "region": {"startLine": 103, "startColumn": 5, "endLine": 103, "endColumn": 8}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 36, "startColumn": 17, "endLine": 36, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S125", "level": "warning", "message": "Remove this commented out code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 144, "startColumn": 13, "endLine": 144, "endColumn": 66}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4487", "level": "warning", "message": "Remove this unread private field '_columnModes' or refactor the code to use its value.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 15, "startColumn": 28, "endLine": 15, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2376", "level": "warning", "message": "Provide a getter for 'ColumnModes' or replace the property with a 'SetColumnModes' method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 50, "startColumn": 27, "endLine": 50, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2292", "level": "warning", "message": "Make this an auto-implemented property and remove its backing field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 36, "startColumn": 25, "endLine": 36, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'private' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/AppConstants.cs", "region": {"startLine": 3, "startColumn": 21, "endLine": 3, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3887", "level": "warning", "message": "Use an immutable collection or reduce the accessibility of the non-private readonly field 'KeyActionDic'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/AppConstants.cs", "region": {"startLine": 156, "startColumn": 28, "endLine": 156, "endColumn": 57}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2386", "level": "warning", "message": "Use an immutable collection or reduce the accessibility of the public static field 'KeyActionDic'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/AppConstants.cs", "region": {"startLine": 156, "startColumn": 28, "endLine": 156, "endColumn": 57}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'IS_EMPTY_CHECK' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DataTypeConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'IS_DISP_CHANGE' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 43}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'NON_DISPLAY_MODE' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'DISPLAY_MODE' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 19, "startColumn": 23, "endLine": 19, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 19, "startColumn": 23, "endLine": 19, "endColumn": 45}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'MAX_STATUS_COUNT' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispConst.cs", "region": {"startLine": 19, "startColumn": 23, "endLine": 19, "endColumn": 39}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispDataFormatConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/EntryTypeConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/FormDispModeConst.cs", "region": {"startLine": 6, "startColumn": 14, "endLine": 6, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/FormPluralModeConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/InputModeConst.cs", "region": {"startLine": 6, "startColumn": 14, "endLine": 6, "endColumn": 28}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 45}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'IS_NOT_EMPTY_CHECK' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/CheckEmptyConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 45}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'IS_NOT_DISP_CHANGE' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/DispChangeConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'NOT_READ_ONLY' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 8, "startColumn": 23, "endLine": 8, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'READ_ONLY' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/ReadOnlyConst.cs", "region": {"startLine": 13, "startColumn": 23, "endLine": 13, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 6, "startColumn": 26, "endLine": 6, "endColumn": 47}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 7, "startColumn": 26, "endLine": 7, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 8, "startColumn": 26, "endLine": 8, "endColumn": 50}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 6, "startColumn": 26, "endLine": 6, "endColumn": 64}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 7, "startColumn": 26, "endLine": 7, "endColumn": 61}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 8, "startColumn": 26, "endLine": 8, "endColumn": 67}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'ERROR_MESSAGE_DISP' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 7, "startColumn": 26, "endLine": 7, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'INFORMATION_MESSAGE_DISP' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 8, "startColumn": 26, "endLine": 8, "endColumn": 50}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'WARNNING_MESSAGE_DISP' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 6, "startColumn": 26, "endLine": 6, "endColumn": 47}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 9, "startColumn": 26, "endLine": 9, "endColumn": 48}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 10, "startColumn": 26, "endLine": 10, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 9, "startColumn": 26, "endLine": 9, "endColumn": 65}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 10, "startColumn": 26, "endLine": 10, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 11, "startColumn": 26, "endLine": 11, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'TRANCEPORT_BUTTON_PUSH' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 9, "startColumn": 26, "endLine": 9, "endColumn": 48}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'DATA_RECIEVE' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 10, "startColumn": 26, "endLine": 10, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/TitleSelectConst.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 11, "startColumn": 26, "endLine": 11, "endColumn": 54}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'LIST_UPDATE' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 11, "startColumn": 26, "endLine": 11, "endColumn": 37}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4144", "level": "warning", "message": "Update this method so that its implementation is not identical to 'ToWindowsMedia'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ColorConverter.cs", "region": {"startLine": 29, "startColumn": 40, "endLine": 29, "endColumn": 55}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Converters/ColorConverter.cs", "region": {"startLine": 20, "startColumn": 40, "endLine": 20, "endColumn": 54}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Update this method so that its implementation is not identical to 'ToSystemDrawing'."}}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptionHelper.cs", "region": {"startLine": 5, "startColumn": 14, "endLine": 5, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4136", "level": "warning", "message": "All 'Invoke' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 5, "startColumn": 26, "endLine": 5, "endColumn": 32}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 28, "startColumn": 24, "endLine": 28, "endColumn": 30}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/CalculateUtility.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ColorHelper.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/DirUtility.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/EncodingUtility.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private field 'LOG_TITLE'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 5, "startColumn": 5, "endLine": 5, "endColumn": 60}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 6, "startColumn": 14, "endLine": 6, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/PropertyHelpers.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 6, "startColumn": 14, "endLine": 6, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1939", "level": "warning", "message": "'int' should not be explicitly used as the underlying type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 5, "startColumn": 33, "endLine": 5, "endColumn": 38}}}], "properties": {"warningLevel": 1, "customProperties": {"redundantIndex": "0"}}}, {"ruleId": "S4136", "level": "warning", "message": "All 'PlaySound' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 21, "startColumn": 31, "endLine": 21, "endColumn": 40}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 42, "startColumn": 31, "endLine": 42, "endColumn": 40}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S4136", "level": "warning", "message": "All 'PlayWavSound' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 75, "startColumn": 24, "endLine": 75, "endColumn": 36}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 88, "startColumn": 24, "endLine": 88, "endColumn": 36}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private method 'ResizeBytesArray'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 272, "startColumn": 27, "endLine": 272, "endColumn": 43}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4144", "level": "warning", "message": "Update this method so that its implementation is not identical to 'Encryption'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 81, "startColumn": 26, "endLine": 81, "endColumn": 36}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 27, "startColumn": 26, "endLine": 27, "endColumn": 36}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Update this method so that its implementation is not identical to 'Decryption'."}}}, {"ruleId": "S2344", "level": "warning", "message": "Rename this enumeration to remove the 'Flags' suffix.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 23, "startColumn": 17, "endLine": 23, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1939", "level": "warning", "message": "'int' should not be explicitly used as the underlying type.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 23, "startColumn": 32, "endLine": 23, "endColumn": 37}}}], "properties": {"warningLevel": 1, "customProperties": {"redundantIndex": "0"}}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S101", "level": "warning", "message": "Rename class 'CPUStatus' to match pascal case naming rules, consider using 'CpuStatus'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 64, "startColumn": 7, "endLine": 64, "endColumn": 16}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private field 'dwLength'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 97, "startColumn": 9, "endLine": 97, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private field 'dwMemoryLoad'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 98, "startColumn": 9, "endLine": 98, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private field 'dwTotalPageFile'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 101, "startColumn": 9, "endLine": 101, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private field 'dwAvailPageFile'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 102, "startColumn": 9, "endLine": 102, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S101", "level": "warning", "message": "Rename class 'MEMORYSTATUS' to match pascal case naming rules, consider using 'Memorystatus'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 95, "startColumn": 19, "endLine": 95, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2211", "level": "note", "message": "Non-constant fields should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 5, "startColumn": 26, "endLine": 5, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 5, "startColumn": 26, "endLine": 5, "endColumn": 59}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2223", "level": "warning", "message": "Change the visibility of 'MENU_BUTTON_PUSH' or make it 'const' or 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Constants/SoundIdConst.cs", "region": {"startLine": 5, "startColumn": 26, "endLine": 5, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 6, "startColumn": 14, "endLine": 6, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/CheckColor.cs", "region": {"startLine": 5, "startColumn": 14, "endLine": 5, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2930", "level": "warning", "message": "Dispose 'objSck' when it is no longer needed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 16, "startColumn": 13, "endLine": 16, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/CheckSound.cs", "region": {"startLine": 13, "startColumn": 14, "endLine": 13, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 12, "startColumn": 17, "endLine": 12, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1104", "level": "warning", "message": "Make this field 'private' and encapsulate it in a 'public' property.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 13, "startColumn": 19, "endLine": 13, "endColumn": 50}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4136", "level": "warning", "message": "All 'Speak' method overloads should be adjacent.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/CheckSound.cs", "region": {"startLine": 19, "startColumn": 24, "endLine": 19, "endColumn": 29}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/CheckSound.cs", "region": {"startLine": 61, "startColumn": 24, "endLine": 61, "endColumn": 29}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Non-adjacent overload"}}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'DbCtl' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 11, "startColumn": 31, "endLine": 11, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'data' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 6, "startColumn": 21, "endLine": 6, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'Logger' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 11, "startColumn": 26, "endLine": 11, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2933", "level": "warning", "message": "Make 'DbCtl' 'readonly'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 12, "startColumn": 31, "endLine": 12, "endColumn": 36}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1118", "level": "warning", "message": "Add a 'protected' constructor or the 'static' keyword to the class declaration.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Utils.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 19}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1144", "level": "warning", "message": "Remove the unused private method 'DeleteOldLogs'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 158, "startColumn": 18, "endLine": 158, "endColumn": 31}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2094", "level": "warning", "message": "Remove this empty class, write its code or make it an \"interface\".", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/UserControlExtension.cs", "region": {"startLine": 3, "startColumn": 21, "endLine": 3, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3928", "level": "warning", "message": "Use a constructor overloads that allows a more meaningful exception message to be provided.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpFileControlComparer.cs", "region": {"startLine": 44, "startColumn": 19, "endLine": 44, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/ListViewItemComparer.cs", "region": {"startLine": 24, "startColumn": 13, "endLine": 25, "endColumn": 14}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2208", "level": "note", "message": "Call the ArgumentException constructor that contains a message and/or paramName parameter", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Comparers/BackUpFileControlComparer.cs", "region": {"startLine": 44, "startColumn": 19, "endLine": 44, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'GetMessage' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 18, "startColumn": 20, "endLine": 18, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptionHelper.cs", "region": {"startLine": 45, "startColumn": 13, "endLine": 45, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'Encrypt' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 42, "startColumn": 20, "endLine": 42, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptionHelper.cs", "region": {"startLine": 82, "startColumn": 13, "endLine": 82, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'LogPerformanceMetrics' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 23, "startColumn": 20, "endLine": 23, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2190", "level": "warning", "message": "Add a way to break out of this method's recursion.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 5, "startColumn": 26, "endLine": 5, "endColumn": 32}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2190", "level": "warning", "message": "Add a way to break out of this method's recursion.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 10, "startColumn": 24, "endLine": 10, "endColumn": 35}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'GetMessage' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 18, "startColumn": 20, "endLine": 18, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'Encrypt' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 42, "startColumn": 20, "endLine": 42, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'LogPerformanceMetrics' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptedTextFormatter.cs", "region": {"startLine": 23, "startColumn": 20, "endLine": 23, "endColumn": 41}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3168", "level": "warning", "message": "Return 'Task' instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/InvokeExtension.cs", "region": {"startLine": 41, "startColumn": 25, "endLine": 41, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptionHelper.cs", "region": {"startLine": 82, "startColumn": 13, "endLine": 82, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Encrypted/EncryptionHelper.cs", "region": {"startLine": 45, "startColumn": 13, "endLine": 45, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3168", "level": "warning", "message": "Return 'Task' instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Extensions/TaskExtensions.cs", "region": {"startLine": 5, "startColumn": 25, "endLine": 5, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1244", "level": "warning", "message": "Do not check floating point equality with exact values, use a range instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ColorHelper.cs", "region": {"startLine": 7, "startColumn": 25, "endLine": 7, "endColumn": 27}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'asm'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 17, "startColumn": 18, "endLine": 17, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3878", "level": "warning", "message": "Remove this array creation and simply pass the elements.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 118, "startColumn": 43, "endLine": 118, "endColumn": 79}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 39 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 63, "startColumn": 25, "endLine": 63, "endColumn": 35}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 71, "startColumn": 13, "endLine": 71, "endColumn": 18}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 75, "startColumn": 17, "endLine": 75, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 75, "startColumn": 60, "endLine": 75, "endColumn": 62}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 78, "startColumn": 21, "endLine": 78, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 83, "startColumn": 25, "endLine": 83, "endColumn": 28}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 87, "startColumn": 29, "endLine": 87, "endColumn": 31}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 92, "startColumn": 29, "endLine": 92, "endColumn": 33}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 98, "startColumn": 25, "endLine": 98, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 104, "startColumn": 21, "endLine": 104, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 112, "startColumn": 25, "endLine": 112, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 118, "startColumn": 25, "endLine": 118, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 122, "startColumn": 25, "endLine": 122, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 127, "startColumn": 25, "endLine": 127, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 140, "startColumn": 9, "endLine": 140, "endColumn": 14}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+2 (incl 1 for nesting)", "10": "+4 (incl 3 for nesting)", "11": "+4 (incl 3 for nesting)", "12": "+4 (incl 3 for nesting)", "13": "+1", "2": "+1", "3": "+3 (incl 2 for nesting)", "4": "+4 (incl 3 for nesting)", "5": "+5 (incl 4 for nesting)", "6": "+1", "7": "+4 (incl 3 for nesting)", "8": "+1", "9": "+4 (incl 3 for nesting)"}}}, {"ruleId": "S3923", "level": "warning", "message": "Remove this conditional structure or edit its code blocks so that they're not all the same.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 157, "startColumn": 17, "endLine": 157, "endColumn": 19}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'SetProcessWorkingSetSize' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/MemoryManagement.cs", "region": {"startLine": 6, "startColumn": 32, "endLine": 6, "endColumn": 56}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "True"}}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 158, "startColumn": 17, "endLine": 159, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 161, "startColumn": 17, "endLine": 162, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'GlobalMemoryStatusEx' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/MemoryManagement.cs", "region": {"startLine": 10, "startColumn": 32, "endLine": 10, "endColumn": 52}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "False"}}}, {"ruleId": "CA1854", "level": "note", "message": "Prefer a 'TryGetValue' call over a Dictionary indexer access guarded by a 'Contains<PERSON>ey' check to avoid double lookup", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 154, "startColumn": 17, "endLine": 154, "endColumn": 44}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 156, "startColumn": 17, "endLine": 156, "endColumn": 47}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1215", "level": "warning", "message": "Refactor the code to remove this use of 'GC.Collect'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/MemoryManagement.cs", "region": {"startLine": 29, "startColumn": 12, "endLine": 29, "endColumn": 19}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1215", "level": "warning", "message": "Refactor the code to remove this use of 'GC.Collect'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/MemoryManagement.cs", "region": {"startLine": 31, "startColumn": 12, "endLine": 31, "endColumn": 19}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2263", "level": "note", "message": "Prefer the generic overload 'System.Runtime.InteropServices.Marshal.SizeOf<T>()' instead of 'System.Runtime.InteropServices.Marshal.SizeOf(System.Type)'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/MemoryManagement.cs", "region": {"startLine": 41, "startColumn": 34, "endLine": 41, "endColumn": 78}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1861", "level": "note", "message": "Prefer 'static readonly' fields over constant array arguments if the called method is called repeatedly and is not mutating the passed array", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 118, "startColumn": 43, "endLine": 118, "endColumn": 79}}}], "properties": {"warningLevel": 1, "customProperties": {"paramName": "trimChars"}}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 20, "startColumn": 9, "endLine": 20, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 20, "startColumn": 27, "endLine": 20, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'r'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ConfigReadUtility.cs", "region": {"startLine": 114, "startColumn": 33, "endLine": 114, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 43, "startColumn": 9, "endLine": 43, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 43, "startColumn": 27, "endLine": 43, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 66, "startColumn": 9, "endLine": 66, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 66, "startColumn": 27, "endLine": 66, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2263", "level": "note", "message": "Prefer the generic overload 'System.Runtime.InteropServices.Marshal.SizeOf<T>()' instead of 'System.Runtime.InteropServices.Marshal.SizeOf(System.Type)'", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/MemoryManagement.cs", "region": {"startLine": 48, "startColumn": 34, "endLine": 48, "endColumn": 78}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 89, "startColumn": 9, "endLine": 89, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 89, "startColumn": 27, "endLine": 89, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 113, "startColumn": 9, "endLine": 113, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 113, "startColumn": 27, "endLine": 113, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 129, "startColumn": 9, "endLine": 129, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/ParseUtility.cs", "region": {"startLine": 129, "startColumn": 27, "endLine": 129, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6667", "level": "warning", "message": "Logging in a catch clause should pass the caught exception as a parameter.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/PropertyHelpers.cs", "region": {"startLine": 51, "startColumn": 13, "endLine": 51, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4200", "level": "warning", "message": "Make this native method private and provide a wrapper.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 17, "startColumn": 31, "endLine": 17, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1401", "level": "note", "message": "P/Invoke method 'MessageBeep' should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 17, "startColumn": 31, "endLine": 17, "endColumn": 42}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'MessageBeep' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 17, "startColumn": 31, "endLine": 17, "endColumn": 42}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "True"}}}, {"ruleId": "S4200", "level": "warning", "message": "Make this native method private and provide a wrapper.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 21, "startColumn": 31, "endLine": 21, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4200", "level": "warning", "message": "Make this native method private and provide a wrapper.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 42, "startColumn": 31, "endLine": 42, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1401", "level": "note", "message": "P/Invoke method 'PlaySound' should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 21, "startColumn": 31, "endLine": 21, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1401", "level": "note", "message": "P/Invoke method 'PlaySound' should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 42, "startColumn": 31, "endLine": 42, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2101", "level": "note", "message": "Specify marshaling for P/Invoke string arguments", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 41, "startColumn": 6, "endLine": 41, "endColumn": 59}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2101", "level": "note", "message": "Specify marshaling for P/Invoke string arguments", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 20, "startColumn": 6, "endLine": 20, "endColumn": 61}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'PlaySound' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 21, "startColumn": 31, "endLine": 21, "endColumn": 40}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "True"}}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'PlaySound' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 42, "startColumn": 31, "endLine": 42, "endColumn": 40}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "True"}}}, {"ruleId": "S3265", "level": "warning", "message": "<PERSON> enum 'PlaySoundFlags' with 'Flags' attribute or remove this bitwise operation.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 104, "startColumn": 71, "endLine": 104, "endColumn": 72}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3265", "level": "warning", "message": "<PERSON> enum 'PlaySoundFlags' with 'Flags' attribute or remove this bitwise operation.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 108, "startColumn": 71, "endLine": 108, "endColumn": 72}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3626", "level": "warning", "message": "Remove this redundant jump.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SoundUtility.cs", "region": {"startLine": 99, "startColumn": 13, "endLine": 99, "endColumn": 20}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'GetIdleTime' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 70, "startColumn": 32, "endLine": 70, "endColumn": 43}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "False"}}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'GetTickCount' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 67, "startColumn": 32, "endLine": 67, "endColumn": 44}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "False"}}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 18, "startColumn": 9, "endLine": 18, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 23, "startColumn": 9, "endLine": 23, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 35, "startColumn": 9, "endLine": 35, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 46, "startColumn": 9, "endLine": 47, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 56, "startColumn": 9, "endLine": 57, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4200", "level": "warning", "message": "Make this native method private and provide a wrapper.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 114, "startColumn": 30, "endLine": 114, "endColumn": 53}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1401", "level": "note", "message": "P/Invoke method 'GetSystemMemoryDivision' should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 114, "startColumn": 30, "endLine": 114, "endColumn": 53}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'GlobalMemoryStatus' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 108, "startColumn": 32, "endLine": 108, "endColumn": 50}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "True"}}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'GetSystemMemoryDivision' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 114, "startColumn": 30, "endLine": 114, "endColumn": 53}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "False"}}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'GetCPU' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 78, "startColumn": 17, "endLine": 78, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S4200", "level": "warning", "message": "Make this native method private and provide a wrapper.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 167, "startColumn": 31, "endLine": 167, "endColumn": 49}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1401", "level": "note", "message": "P/Invoke method 'GetDiskFreeSpaceEx' should not be visible", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 167, "startColumn": 31, "endLine": 167, "endColumn": 49}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2101", "level": "note", "message": "Specify marshaling for P/Invoke string arguments", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 166, "startColumn": 6, "endLine": 166, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 18, "startColumn": 15, "endLine": 18, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 23, "startColumn": 15, "endLine": 23, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 35, "startColumn": 15, "endLine": 35, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 47, "startColumn": 9, "endLine": 47, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 57, "startColumn": 9, "endLine": 57, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "SYSLIB1054", "level": "note", "message": "Mark the method 'GetDiskFreeSpaceEx' with 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 167, "startColumn": 31, "endLine": 167, "endColumn": 49}}}], "properties": {"warningLevel": 1, "customProperties": {"CharSet": "None", "ExactSpelling": "False", "MayRequireAdditionalWork": "True"}}}, {"ruleId": "S2696", "level": "warning", "message": "Make the enclosing instance method 'static' or remove this set on the 'static' field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 84, "startColumn": 9, "endLine": 84, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 72, "startColumn": 25, "endLine": 72, "endColumn": 40}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null}}}, {"ruleId": "S2696", "level": "warning", "message": "Make the enclosing instance method 'static' or remove this set on the 'static' field.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 85, "startColumn": 9, "endLine": 85, "endColumn": 19}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 73, "startColumn": 25, "endLine": 73, "endColumn": 37}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null}}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'GetCPU' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 78, "startColumn": 17, "endLine": 78, "endColumn": 23}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1859", "level": "note", "message": "Change return type of method 'ResolveAsCommand' from 'object?' to 'Microsoft.Maui.Controls.Command?' for improved performance", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/PropertyHelpers.cs", "region": {"startLine": 66, "startColumn": 28, "endLine": 66, "endColumn": 44}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 29, "startColumn": 13, "endLine": 29, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6562", "level": "warning", "message": "Provide the \"DateTimeKind\" when creating this object.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 55, "startColumn": 31, "endLine": 55, "endColumn": 55}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 84, "startColumn": 13, "endLine": 84, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 29, "startColumn": 31, "endLine": 29, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'res'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 211, "startColumn": 14, "endLine": 211, "endColumn": 17}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 84, "startColumn": 31, "endLine": 84, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'valmstat'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 27, "startColumn": 18, "endLine": 27, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'valmstatVirtual'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 38, "startColumn": 18, "endLine": 38, "endColumn": 33}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'valdstat'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 51, "startColumn": 18, "endLine": 51, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'Reader'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 38, "startColumn": 13, "endLine": 38, "endColumn": 26}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 27, "startColumn": 9, "endLine": 29, "endColumn": 10}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3445", "level": "warning", "message": "Consider using 'throw;' to preserve the stack trace.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 29, "startColumn": 13, "endLine": 29, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 28, "startColumn": 9, "endLine": 29, "endColumn": 10}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Utils.cs", "region": {"startLine": 19, "startColumn": 9, "endLine": 19, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'ipHost'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Utils.cs", "region": {"startLine": 10, "startColumn": 34, "endLine": 10, "endColumn": 78}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3010", "level": "warning", "message": "Remove this assignment of 'WriteCount' or initialize it statically.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 22, "startColumn": 9, "endLine": 22, "endColumn": 21}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 16, "startColumn": 24, "endLine": 16, "endColumn": 40}}}], "properties": {"warningLevel": 1, "customProperties": {"0": null}}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Utils.cs", "region": {"startLine": 19, "startColumn": 15, "endLine": 19, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2200", "level": "warning", "message": "Re-throwing caught exception changes stack information", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/Configration.cs", "region": {"startLine": 29, "startColumn": 13, "endLine": 29, "endColumn": 21}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1481", "level": "warning", "message": "Remove the unused local variable 'res'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SpecUtility.cs", "region": {"startLine": 150, "startColumn": 13, "endLine": 150, "endColumn": 16}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 256, "startColumn": 9, "endLine": 258, "endColumn": 10}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 223, "startColumn": 17, "endLine": 225, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2486", "level": "warning", "message": "Handle the exception or explain in a comment why it can be ignored.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 228, "startColumn": 9, "endLine": 230, "endColumn": 10}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 224, "startColumn": 17, "endLine": 225, "endColumn": 18}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 229, "startColumn": 9, "endLine": 230, "endColumn": 10}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 59, "startColumn": 35, "endLine": 59, "endColumn": 40}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 61, "startColumn": 68, "endLine": 61, "endColumn": 73}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.IndexOf(char)' instead of 'string.IndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 61, "startColumn": 110, "endLine": 61, "endColumn": 115}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1075", "level": "warning", "message": "Remove this hardcoded path-delimiter.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 213, "startColumn": 52, "endLine": 213, "endColumn": 56}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA2249", "level": "note", "message": "Use 'string.Contains' instead of 'string.IndexOf' to improve readability", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/StringUtility.cs", "region": {"startLine": 59, "startColumn": 21, "endLine": 59, "endColumn": 46}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'MacCatalyst' 15.0 and later. 'DateAndTime.TimeOfDay.set' is only supported on: 'windows'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 64, "startColumn": 13, "endLine": 64, "endColumn": 34}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1416", "level": "warning", "message": "This call site is reachable on: 'MacCatalyst' 15.0 and later. 'DateAndTime.Today.set' is only supported on: 'windows'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/TimeUtility.cs", "region": {"startLine": 63, "startColumn": 13, "endLine": 63, "endColumn": 30}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S108", "level": "warning", "message": "Either remove or fill this block of code.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 257, "startColumn": 9, "endLine": 258, "endColumn": 10}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1866", "level": "note", "message": "Use 'string.LastIndexOf(char)' instead of 'string.LastIndexOf(string)' when you have a string with a single char", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/FileOut.cs", "region": {"startLine": 156, "startColumn": 45, "endLine": 156, "endColumn": 51}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1854", "level": "warning", "message": "Remove this useless assignment to local variable 'retByte'.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 123, "startColumn": 16, "endLine": 123, "endColumn": 50}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1643", "level": "warning", "message": "Use a StringBuilder instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Helpers/SecureUtility.cs", "region": {"startLine": 65, "startColumn": 13, "endLine": 65, "endColumn": 75}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'GetIp' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 76, "startColumn": 20, "endLine": 76, "endColumn": 25}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S3776", "level": "warning", "message": "Refactor this method to reduce its Cognitive Complexity from 24 to the 15 allowed.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 91, "startColumn": 17, "endLine": 91, "endColumn": 22}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 95, "startColumn": 13, "endLine": 95, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 99, "startColumn": 17, "endLine": 99, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 104, "startColumn": 21, "endLine": 104, "endColumn": 23}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 106, "startColumn": 25, "endLine": 106, "endColumn": 32}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 111, "startColumn": 21, "endLine": 111, "endColumn": 25}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 114, "startColumn": 25, "endLine": 114, "endColumn": 27}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 122, "startColumn": 29, "endLine": 122, "endColumn": 31}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 133, "startColumn": 13, "endLine": 133, "endColumn": 15}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 136, "startColumn": 17, "endLine": 136, "endColumn": 19}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 147, "startColumn": 9, "endLine": 147, "endColumn": 14}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "+1", "1": "+2 (incl 1 for nesting)", "2": "+3 (incl 2 for nesting)", "3": "+4 (incl 3 for nesting)", "4": "+1", "5": "+4 (incl 3 for nesting)", "6": "+5 (incl 4 for nesting)", "7": "+1", "8": "+2 (incl 1 for nesting)", "9": "+1"}}}, {"ruleId": "S2325", "level": "warning", "message": "Make 'Flush' a static method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 153, "startColumn": 17, "endLine": 153, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'Flush' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 153, "startColumn": 17, "endLine": 153, "endColumn": 22}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CA1824", "level": "note", "message": "Mark assemblies with NeutralResourcesLanguageAttribute", "properties": {"warningLevel": 1}}, {"ruleId": "CA1822", "level": "note", "message": "Member 'GetIp' does not access instance data and can be marked as static", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Presentation/UI/App/ShinKenShinKun.Utils/Others/ClientLogger.cs", "region": {"startLine": 76, "startColumn": 20, "endLine": 76, "endColumn": 25}}}], "properties": {"warningLevel": 1}}], "rules": {"CA1401": {"id": "CA1401", "shortDescription": "P/Invokes should not be visible", "fullDescription": "A public or protected method in a public type has the System.Runtime.InteropServices.DllImportAttribute attribute (also implemented by the Declare keyword in Visual Basic). Such methods should not be exposed.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1401", "properties": {"category": "Interoperability", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1416": {"id": "CA1416", "shortDescription": "Validate platform compatibility", "fullDescription": "Using platform dependent API on a component makes the code no longer work across all platforms.", "defaultLevel": "warning", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1416", "properties": {"category": "Interoperability", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1822": {"id": "CA1822", "shortDescription": "Mark members as static", "fullDescription": "Members that do not access instance data or call instance methods can be marked as static. After you mark the methods as static, the compiler will emit nonvirtual call sites to these members. This can give you a measurable performance gain for performance-sensitive code.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1822", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1824": {"id": "CA1824", "shortDescription": "Mark assemblies with NeutralResourcesLanguageAttribute", "fullDescription": "The NeutralResourcesLanguage attribute informs the ResourceManager of the language that was used to display the resources of a neutral culture for an assembly. This improves lookup performance for the first resource that you load and can reduce your working set.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1824", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1854": {"id": "CA1854", "shortDescription": "Prefer the 'IDictionary.TryGetValue(TKey, out TValue)' method", "fullDescription": "Prefer a 'TryGetValue' call over a Dictionary indexer access guarded by a 'Contains<PERSON>ey' check. 'Contains<PERSON><PERSON>' and the indexer both would lookup the key under the hood, so using 'TryGetValue' removes the extra lookup.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1854", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1859": {"id": "CA1859", "shortDescription": "Use concrete types when possible for improved performance", "fullDescription": "Using concrete types avoids virtual or interface call overhead and enables inlining.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1859", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1861": {"id": "CA1861", "shortDescription": "Avoid constant arrays as arguments", "fullDescription": "Constant arrays passed as arguments are not reused when called repeatedly, which implies a new array is created each time. Consider extracting them to 'static readonly' fields to improve performance if the passed array is not mutated within the called method.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1861", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA1866": {"id": "CA1866", "shortDescription": "Use char overload", "fullDescription": "The char overload is a better performing overload than a string with a single char.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1866", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2101": {"id": "CA2101", "shortDescription": "Specify marshaling for P/Invoke string arguments", "fullDescription": "A platform invoke member allows partially trusted callers, has a string parameter, and does not explicitly marshal the string. This can cause a potential security vulnerability.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2101", "properties": {"category": "Globalization", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2200": {"id": "CA2200", "shortDescription": "Rethrow to preserve stack details", "defaultLevel": "warning", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2200", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2208": {"id": "CA2208", "shortDescription": "Instantiate argument exceptions correctly", "fullDescription": "A call is made to the default (parameterless) constructor of an exception type that is or derives from ArgumentException, or an incorrect string argument is passed to a parameterized constructor of an exception type that is or derives from ArgumentException.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2208", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2211": {"id": "CA2211", "shortDescription": "Non-constant fields should not be visible", "fullDescription": "Static fields that are neither constants nor read-only are not thread-safe. Access to such a field must be carefully controlled and requires advanced programming techniques to synchronize access to the class object.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2211", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["PortedFromFxCop", "Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2249": {"id": "CA2249", "shortDescription": "Consider using 'string.Contains' instead of 'string.IndexOf'", "fullDescription": "Calls to 'string.IndexOf' where the result is used to check for the presence/absence of a substring can be replaced by 'string.Contains'.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2249", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CA2263": {"id": "CA2263", "shortDescription": "Prefer generic overload when type is known", "fullDescription": "Using a generic overload is preferable to the 'System.Type' overload when the type is known, promoting cleaner and more type-safe code with improved compile-time checks.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca2263", "properties": {"category": "Usage", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CS0162": {"id": "CS0162", "shortDescription": "Unreachable code detected", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS0162)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS0168": {"id": "CS0168", "shortDescription": "Variable is declared but never used", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS0168)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS0219": {"id": "CS0219", "shortDescription": "Variable is assigned but its value is never used", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS0219)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8600": {"id": "CS8600", "shortDescription": "Converting null literal or possible null value to non-nullable type.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8600)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8602": {"id": "CS8602", "shortDescription": "Dereference of a possibly null reference.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8602)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8603": {"id": "CS8603", "shortDescription": "Possible null reference return.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8603)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8604": {"id": "CS8604", "shortDescription": "Possible null reference argument.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8604)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8618": {"id": "CS8618", "shortDescription": "Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8618)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8625": {"id": "CS8625", "shortDescription": "Cannot convert null literal to non-nullable reference type.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8625)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8629": {"id": "CS8629", "shortDescription": "Nullable value type may be null.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8629)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8767": {"id": "CS8767", "shortDescription": "Nullability of reference types in type of parameter doesn't match implicitly implemented member (possibly because of nullability attributes).", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8767)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "S101": {"id": "S101", "shortDescription": "Types should be named in PascalCase", "fullDescription": "Shared naming conventions allow teams to collaborate efficiently.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-101", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1075": {"id": "S1075", "shortDescription": "URIs should not be hardcoded", "fullDescription": "Hard-coding a URI makes it difficult to test a program for a variety of reasons:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1075", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S108": {"id": "S108", "shortDescription": "Nested blocks of code should not be left empty", "fullDescription": "An empty code block is confusing. It will require some effort from maintainers to determine if it is intentional or indicates the implementation is incomplete.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-108", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1104": {"id": "S1104", "shortDescription": "Fields should not have public accessibility", "fullDescription": "Public fields in public classes do not respect the encapsulation principle and have three main disadvantages:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1104", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S1118": {"id": "S1118", "shortDescription": "Utility classes should not have public constructors", "fullDescription": "Whenever there are portions of code that are duplicated and do not depend on the state of their container class, they can be centralized inside a \"utility class\". A utility class is a class that only has static members, hence it should not be instantiated.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1118", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1135": {"id": "S1135", "shortDescription": "Track uses of \"TODO\" tags", "fullDescription": "Developers often use TODO tags to mark areas in the code where additional work or improvements are needed but are not implemented immediately. However, these TODO tags sometimes get overlooked or forgotten, leading to incomplete or unfinished code. This rule aims to identify and address unattended TODO tags to ensure a clean and maintainable codebase. This description explores why this is a problem and how it can be fixed to improve the overall code quality.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1135", "properties": {"category": "Info Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1144": {"id": "S1144", "shortDescription": "Unused private types or members should be removed", "fullDescription": "This rule raises an issue when a private/internal type or member is never referenced in the code.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1144", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1215": {"id": "S1215", "shortDescription": "\"GC.Collect\" should not be called", "fullDescription": "GC.Collect is a method that forces or suggests to the garbage collector to run a collection of objects in the managed heap that are no longer being used and free their memory.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1215", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1244": {"id": "S1244", "shortDescription": "Floating point numbers should not be tested for equality", "fullDescription": "Floating point numbers in C# (and in most other programming languages) are not precise. They are a binary approximation of the actual value. This means that even if two floating point numbers appear to be equal, they might not be due to the tiny differences in their binary representation.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1244", "properties": {"category": "Major Bug", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S125": {"id": "S125", "shortDescription": "Sections of code should not be commented out", "fullDescription": "Commented-out code distracts the focus from the actual executed code. It creates a noise that increases maintenance code. And because it is never executed, it quickly becomes out of date and invalid.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-125", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1481": {"id": "S1481", "shortDescription": "Unused local variables should be removed", "fullDescription": "An unused local variable is a variable that has been declared but is not used anywhere in the block of code where it is defined. It is dead code, contributing to unnecessary complexity and leading to confusion when reading the code. Therefore, it should be removed from your code to maintain clarity and efficiency.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1481", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1643": {"id": "S1643", "shortDescription": "Strings should not be concatenated using '+' in a loop", "fullDescription": "Concatenating multiple string literals or strings using the + operator creates a new string object for each concatenation. This can lead to a large number of intermediate string objects and can be inefficient. The StringBuilder class is more efficient than string concatenation, especially when the operator is repeated over and over as in loops.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1643", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S1854": {"id": "S1854", "shortDescription": "Unused assignments should be removed", "fullDescription": "Dead stores refer to assignments made to local variables that are subsequently never used or immediately overwritten. Such assignments are unnecessary and don’t contribute to the functionality or clarity of the code. They may even negatively impact performance. Removing them enhances code cleanliness and readability. Even if the unnecessary operations do not do any harm in terms of the program’s correctness, they are - at best - a waste of computing resources.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1854", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S1939": {"id": "S1939", "shortDescription": "Inheritance list should not be redundant", "fullDescription": "An inheritance list entry is redundant if:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1939", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2094": {"id": "S2094", "shortDescription": "Classes should not be empty", "fullDescription": "There is no good excuse for an empty class. If it’s being used simply as a common extension point, it should be replaced with an interface. If it was stubbed in as a placeholder for future development it should be fleshed-out. In any other case, it should be eliminated.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2094", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2190": {"id": "S2190", "shortDescription": "Loops and recursions should not be infinite", "fullDescription": "Having an infinite loop or recursion will lead to a program failure or a program never finishing the execution.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2190", "properties": {"category": "Blocker Bug", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2223": {"id": "S2223", "shortDescription": "Non-constant static fields should not be visible", "fullDescription": "Unlike instance fields, which can only be accessed by code having a hold on the instance, static fields can be accessed by any code having visibility of the field and its type.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2223", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2292": {"id": "S2292", "shortDescription": "Trivial properties should be auto-implemented", "fullDescription": "Trivial properties, which include no logic but setting and getting a backing field should be converted to auto-implemented properties, yielding cleaner and more readable code.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2292", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2325": {"id": "S2325", "shortDescription": "Methods and properties that don't access instance data should be static", "fullDescription": "Methods and properties that don’t access instance data should be marked as static for the following reasons:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2325", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2344": {"id": "S2344", "shortDescription": "Enumeration type names should not have \"Flags\" or \"Enum\" suffixes", "fullDescription": "The information that an enumeration type is actually an enumeration or a set of flags should not be duplicated in its name.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2344", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2376": {"id": "S2376", "shortDescription": "Write-only properties should not be used", "fullDescription": "Properties with only setters are confusing and counterintuitive. Instead, a property getter should be added if possible, or the property should be replaced with a setter method.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2376", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2386": {"id": "S2386", "shortDescription": "Mutable fields should not be \"public static\"", "fullDescription": "public static mutable fields of classes which are accessed directly should be protected to the degree possible. This can be done by reducing the accessibility of the field or by changing the return type to an immutable type.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2386", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2486": {"id": "S2486", "shortDescription": "Generic exceptions should not be ignored", "fullDescription": "When exceptions occur, it is usually a bad idea to simply ignore them. Instead, it is better to handle them properly, or at least to log them.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2486", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2696": {"id": "S2696", "shortDescription": "Instance members should not write to \"static\" fields", "fullDescription": "This rule raises an issue each time a static field is updated from a non-static method or property.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2696", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2930": {"id": "S2930", "shortDescription": "\"IDisposables\" should be disposed", "fullDescription": "When writing managed code, there is no need to worry about memory allocation or deallocation as it is taken care of by the garbage collector. However, certain objects, such as Bitmap, utilize unmanaged memory for specific purposes like pointer arithmetic. These objects may have substantial unmanaged memory footprints while having minimal managed footprints. Unfortunately, the garbage collector only recognizes the small managed footprint and does not promptly reclaim the corresponding unmanaged memory (by invoking the finalizer method of Bitmap) for efficiency reasons.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2930", "properties": {"category": "Blocker Bug", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2933": {"id": "S2933", "shortDescription": "Fields that are only assigned in the constructor should be \"readonly\"", "fullDescription": "readonly fields can only be assigned in a class constructor. If a class has a field that’s not marked readonly but is only set in the constructor, it could cause confusion about the field’s intended use. To avoid confusion, such fields should be marked readonly to make their intended use explicit, and to prevent future maintainers from inadvertently changing their use.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2933", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3010": {"id": "S3010", "shortDescription": "Static fields should not be updated in constructors", "fullDescription": "Assigning a value to a static field in a constructor could cause unreliable behavior at runtime since it will change the value for all instances of the class.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3010", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S3168": {"id": "S3168", "shortDescription": "\"async\" methods should not return \"void\"", "fullDescription": "An async method with a void return type does not follow the task asynchronous programming (TAP) model since the return type should be Task or Task<TResult>", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3168", "properties": {"category": "Major Bug", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3260": {"id": "S3260", "shortDescription": "Non-derived \"private\" classes and records should be \"sealed\"", "fullDescription": "Classes and records with either private or file access modifiers aren’t visible outside of their assemblies or files, so if they’re not extended inside their scope, they should be made explicitly non-extensible with the addition of the sealed keyword.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3260", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S3265": {"id": "S3265", "shortDescription": "Non-flags enums should not be used in bitwise operations", "fullDescription": "Enumerations are commonly used to identify distinct elements from a set of values.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3265", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3445": {"id": "S3445", "shortDescription": "Exceptions should not be explicitly rethrown", "fullDescription": "In C#, the throw statement can be used in two different ways:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3445", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S3626": {"id": "S3626", "shortDescription": "Jump statements should not be redundant", "fullDescription": "Jump statements, such as return, yield break, goto, and continue let you change the default flow of program execution, but jump statements that direct the control flow to the original direction are just a waste of keystrokes.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3626", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3776": {"id": "S3776", "shortDescription": "Cognitive Complexity of methods should not be too high", "fullDescription": "This rule raises an issue when the code cognitive complexity of a function is above a certain threshold.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3776", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": false, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3878": {"id": "S3878", "shortDescription": "Arrays should not be created for params parameters", "fullDescription": "Creating an array or using a collection expression solely for the purpose of passing it to a params parameter is unnecessary. Simply pass the elements directly, and they will be automatically consolidated into the appropriate collection type.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3878", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3887": {"id": "S3887", "shortDescription": "Mutable, non-private fields should not be \"readonly\"", "fullDescription": "Using the readonly keyword on a field means it can’t be changed after initialization. However, that’s only partly true when applied to collections or arrays. The readonly keyword enforces that another instance can’t be assigned to the field, but it cannot keep the contents from being updated. In practice, the field value can be changed, and the use of readonly on such a field is misleading, and you’re likely not getting the behavior you expect.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3887", "properties": {"category": "Minor Bug", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3923": {"id": "S3923", "shortDescription": "All branches in a conditional structure should not have exactly the same implementation", "fullDescription": "Having all branches of a switch or if chain with the same implementation indicates a problem.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3923", "properties": {"category": "Major Bug", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S3928": {"id": "S3928", "shortDescription": "Parameter names used into ArgumentException constructors should match an existing one ", "fullDescription": "Some constructors of the ArgumentException, ArgumentNullException, ArgumentOutOfRangeException and DuplicateWaitObjectException classes must be fed with a valid parameter name. This rule raises an issue in two cases:", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-3928", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S4136": {"id": "S4136", "shortDescription": "Method overloads should be grouped together", "fullDescription": "For clarity, all overloads of the same method should be grouped together. That lets both users and maintainers quickly understand all the current available options.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4136", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S4144": {"id": "S4144", "shortDescription": "Methods should not have identical implementations", "fullDescription": "Two methods having the same implementation are suspicious. It might be that something else was intended. Or the duplication is intentional, which becomes a maintenance burden.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4144", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S4200": {"id": "S4200", "shortDescription": "Native methods should be wrapped", "fullDescription": "Native methods are functions that reside in libraries outside the .NET runtime. Calling them is helpful for interoperability with applications and libraries written in other programming languages, mainly when performing platform-specific operations. However, doing so comes with additional risks since it means stepping out of the memory-safety model of the runtime. It is therefore highly recommended to take extra steps, like input validation, when invoking native methods. Making the native method private and providing a wrapper that performs these additional steps is the best way to do so.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4200", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S4487": {"id": "S4487", "shortDescription": "Unread \"private\" fields should be removed", "fullDescription": "Private fields which are written but never read are a case of \"dead store\". Changing the value of such a field is useless and most probably indicates an error in the code.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-4487", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S6562": {"id": "S6562", "shortDescription": "Always set the \"DateTimeKind\" when creating new \"DateTime\" instances", "fullDescription": "Not knowing the Kind of the DateTime object that an application is using can lead to misunderstandings when displaying or comparing them. Explicitly setting the Kind property helps the application to stay consistent, and its maintainers understand what kind of date is being managed. To achieve this, when instantiating a new DateTime object you should always use a constructor overload that allows you to define the Kind property.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-6562", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S6667": {"id": "S6667", "shortDescription": "Logging in a catch clause should pass the caught exception as a parameter.", "fullDescription": "This rule raises an issue on logging calls inside a catch clause that does not pass the raised Exception.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-6667", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "SYSLIB0044": {"id": "SYSLIB0044", "shortDescription": "Type or member is obsolete", "defaultLevel": "warning", "helpUri": "https://aka.ms/dotnet-warnings/SYSLIB0044", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry", "CustomObsolete"]}}, "SYSLIB1054": {"id": "SYSLIB1054", "shortDescription": "Use 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "fullDescription": "Use 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/syslib-diagnostics/syslib1054", "properties": {"category": "Interoperability", "isEnabledByDefault": true}}}}]}