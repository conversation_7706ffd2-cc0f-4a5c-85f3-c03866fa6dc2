<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>Domain</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>73a0e841-8070-20b3-947f-ec7ddab1b405</ProjectGuid>
  <FullPath>C:\Data\git\arcteckenshinkunvup2024\src\Core\Domain\Domain.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Data\git\arcteckenshinkunvup2024\.sonarqube\conf\1\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\1\Issues.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPaths">C:\Data\git\arcteckenshinkunvup2024\.sonarqube\out\1</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
  <TargetFramework>net9.0</TargetFramework>
</ProjectInfo>