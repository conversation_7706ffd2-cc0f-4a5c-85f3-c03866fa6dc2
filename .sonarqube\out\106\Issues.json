{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "CS8603", "level": "warning", "message": "Possible null reference return.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Identity/IdentityService.cs", "region": {"startLine": 23, "startColumn": 16, "endLine": 23, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8620", "level": "warning", "message": "Argument of type 'string?[]' cannot be used for parameter 'roles' of type 'IEnumerable<string>' in 'Task<IdentityResult> UserManager<ApplicationUser>.AddToRolesAsync(ApplicationUser user, IEnumerable<string> roles)' due to differences in the nullability of reference types.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 63, "startColumn": 63, "endLine": 63, "endColumn": 95}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2094", "level": "warning", "message": "Remove this empty class, write its code or make it an \"interface\".", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Identity/ApplicationUser.cs", "region": {"startLine": 3, "startColumn": 14, "endLine": 3, "endColumn": 29}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1699", "level": "warning", "message": "Remove this call from a constructor to the overridable 'AutoMap' method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Files/Maps/TodoItemRecordMap.cs", "region": {"startLine": 7, "startColumn": 9, "endLine": 7, "endColumn": 16}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S1699", "level": "warning", "message": "Remove this call from a constructor to the overridable 'Map' method.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Files/Maps/TodoItemRecordMap.cs", "region": {"startLine": 9, "startColumn": 9, "endLine": 9, "endColumn": 12}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S2139", "level": "warning", "message": "Either log this exception and handle it, or rethrow it with some contextual information.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 40, "startColumn": 26, "endLine": 40, "endColumn": 28}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 42, "startColumn": 13, "endLine": 42, "endColumn": 82}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 43, "startColumn": 13, "endLine": 43, "endColumn": 19}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Logging statement.", "1": "Thrown exception."}}}, {"ruleId": "S2139", "level": "warning", "message": "Either log this exception and handle it, or rethrow it with some contextual information.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 27, "startColumn": 26, "endLine": 27, "endColumn": 28}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 29, "startColumn": 13, "endLine": 29, "endColumn": 87}}}, {"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 30, "startColumn": 13, "endLine": 30, "endColumn": 19}}}], "properties": {"warningLevel": 1, "customProperties": {"0": "Logging statement.", "1": "Thrown exception."}}}, {"ruleId": "CA1860", "level": "note", "message": "Prefer comparing 'Count' to 0 rather than using 'Any()', both for clarity and for performance", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Common/MediatorExtensions.cs", "region": {"startLine": 9, "startColumn": 25, "endLine": 9, "endColumn": 52}}}], "properties": {"warningLevel": 1, "customProperties": {"DiagnosticPropertyKey": "Count"}}}, {"ruleId": "S6966", "level": "warning", "message": "Await SingleOrDefaultAsync instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Identity/IdentityService.cs", "region": {"startLine": 48, "startColumn": 20, "endLine": 48, "endColumn": 75}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6966", "level": "warning", "message": "Await SingleOrDefaultAsync instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Identity/IdentityService.cs", "region": {"startLine": 41, "startColumn": 20, "endLine": 41, "endColumn": 75}}}], "properties": {"warningLevel": 1}}, {"ruleId": "ASP0025", "level": "note", "message": "Use AddAuthorizationBuilder to register authorization services and construct policies", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/ConfigureServices.cs", "region": {"startLine": 41, "startColumn": 9, "endLine": 42, "endColumn": 90}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6966", "level": "warning", "message": "Await <PERSON>Async instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 52, "startColumn": 13, "endLine": 52, "endColumn": 74}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6966", "level": "warning", "message": "Await <PERSON>Async instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 60, "startColumn": 13, "endLine": 60, "endColumn": 78}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6966", "level": "warning", "message": "Await <PERSON><PERSON><PERSON> instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Persistence/ApplicationDbContextInitialiser.cs", "region": {"startLine": 68, "startColumn": 14, "endLine": 68, "endColumn": 38}}}], "properties": {"warningLevel": 1}}, {"ruleId": "S6966", "level": "warning", "message": "Await SingleOrDefaultAsync instead.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Infrastructure/Identity/IdentityService.cs", "region": {"startLine": 64, "startColumn": 20, "endLine": 64, "endColumn": 75}}}], "properties": {"warningLevel": 1}}], "rules": {"ASP0025": {"id": "ASP0025", "shortDescription": "Use AddAuthorizationBuilder", "defaultLevel": "note", "helpUri": "https://aka.ms/aspnet/analyzers", "properties": {"category": "Usage", "isEnabledByDefault": true}}, "CA1860": {"id": "CA1860", "shortDescription": "Avoid using 'Enumerable.Any()' extension method", "fullDescription": "Prefer using 'IsEmpty', 'Count' or 'Length' properties whichever available, rather than calling 'Enumerable.Any()'. The intent is clearer and it is more performant than using 'Enumerable.Any()' extension method.", "defaultLevel": "note", "helpUri": "https://learn.microsoft.com/dotnet/fundamentals/code-analysis/quality-rules/ca1860", "properties": {"category": "Performance", "isEnabledByDefault": true, "tags": ["Telemetry", "EnabledRuleInAggressiveMode"]}}, "CS8603": {"id": "CS8603", "shortDescription": "Possible null reference return.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8603)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "CS8620": {"id": "CS8620", "shortDescription": "Argument cannot be used for parameter due to differences in the nullability of reference types.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8620)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}, "S1699": {"id": "S1699", "shortDescription": "Constructors should only call non-overridable methods", "fullDescription": "Calling an overridable method from a constructor could result in failures or strange behaviors when instantiating a subclass which overrides the method.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-1699", "properties": {"category": "Critical Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S2094": {"id": "S2094", "shortDescription": "Classes should not be empty", "fullDescription": "There is no good excuse for an empty class. If it’s being used simply as a common extension point, it should be replaced with an interface. If it was stubbed in as a placeholder for future development it should be fleshed-out. In any other case, it should be eliminated.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2094", "properties": {"category": "Minor Code Smell", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}, "S2139": {"id": "S2139", "shortDescription": "Exceptions should be either logged or rethrown but not both", "fullDescription": "When an exception is logged and rethrown, the upstream code may not be aware that the exception has already been logged. As a result, the same exception gets logged multiple times, making it difficult to identify the root cause of the issue. This can be particularly problematic in multi-threaded applications where messages from other threads can be interwoven with the repeated log entries.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-2139", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "SonarWay"]}}, "S6966": {"id": "S6966", "shortDescription": "Awaitable method should be used", "fullDescription": "In an async method, any blocking operations should be avoided.", "defaultLevel": "warning", "helpUri": "https://rules.sonarsource.com/csharp/RSPEC-6966", "properties": {"category": "Major <PERSON>", "isEnabledByDefault": true, "tags": ["C#", "MainSourceScope", "TestSourceScope", "SonarWay"]}}}}]}