{"$schema": "http://json.schemastore.org/sarif-1.0.0", "version": "1.0.0", "runs": [{"tool": {"name": "Microsoft (R) Visual C# Compiler", "version": "4.14.0.0", "fileVersion": "4.14.0-3.25218.8 (d7bde97e)", "semanticVersion": "4.14.0", "language": "en-US"}, "results": [{"ruleId": "CS8618", "level": "warning", "message": "Non-nullable property 'Title' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Common/Shared/TodoLists/TodoListSummaryDto.cs", "region": {"startLine": 6, "startColumn": 19, "endLine": 6, "endColumn": 24}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Common/Shared/TodoLists/TodoListSummaryDto.cs", "region": {"startLine": 6, "startColumn": 19, "endLine": 6, "endColumn": 24}}}], "properties": {"warningLevel": 1}}, {"ruleId": "CS8618", "level": "warning", "message": "Non-nullable property 'Title' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.", "locations": [{"resultFile": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Common/Shared/TodoLists/NewTodoDto.cs", "region": {"startLine": 4, "startColumn": 19, "endLine": 4, "endColumn": 24}}}], "relatedLocations": [{"physicalLocation": {"uri": "file:///C:/Data/git/arcteckenshinkunvup2024/src/Common/Shared/TodoLists/NewTodoDto.cs", "region": {"startLine": 4, "startColumn": 19, "endLine": 4, "endColumn": 24}}}], "properties": {"warningLevel": 1}}], "rules": {"CS8618": {"id": "CS8618", "shortDescription": "Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.", "defaultLevel": "warning", "helpUri": "https://msdn.microsoft.com/query/roslyn.query?appId=roslyn&k=k(CS8618)", "properties": {"category": "Compiler", "isEnabledByDefault": true, "tags": ["Compiler", "Telemetry"]}}}}]}